---
// Componente del botón hamburguesa
interface Props {
	id?: string;
	class?: string;
	targetDrawer?: string;
}

const {
	id = "burger-btn",
	class: className = "",
	targetDrawer = "mobile-drawer",
} = Astro.props;
---

<button
	id={id}
	class={`md:hidden flex flex-col justify-center items-center w-8 h-8 transition-all duration-300 ${className}`}
	aria-label="Abrir menú de navegación"
	data-drawer-target={targetDrawer}
>
	<span
		class="burger-line block w-6 h-0.5 bg-gray-700 transition-all duration-300 origin-center"
	></span>
	<span
		class="burger-line block w-6 h-0.5 bg-gray-700 transition-all duration-300 origin-center my-1"
	></span>
	<span
		class="burger-line block w-6 h-0.5 bg-gray-700 transition-all duration-300 origin-center"
	></span>
</button>

<style>
	/* Estado inicial - eliminamos space-y-1 para mejor control */
	.burger-line {
		transform-origin: center;
	}

	/* Estado activo - X perfectamente centrada */
	.burger-active .burger-line:nth-child(1) {
		transform: translateY(6px) rotate(45deg);
	}

	.burger-active .burger-line:nth-child(2) {
		opacity: 0;
		transform: scaleX(0);
	}

	.burger-active .burger-line:nth-child(3) {
		transform: translateY(-6px) rotate(-45deg);
	}
</style>

<script>
	// Script del botón hamburguesa
	document.addEventListener("DOMContentLoaded", () => {
		const burgerButtons = document.querySelectorAll("[data-drawer-target]");

		burgerButtons.forEach((burgerBtn) => {
			const targetDrawer = burgerBtn.getAttribute("data-drawer-target");
			const drawer = document.getElementById(targetDrawer);
			const overlay = document.getElementById(`${targetDrawer}-overlay`);

			const divDrawer = overlay?.parentElement;

			if (!drawer || !overlay) return;

			// Función para abrir el drawer
			function openDrawer() {
				drawer.classList.add("drawer-open");
				overlay.classList.remove("hidden");
				overlay.classList.add("drawer-overlay-visible");
				divDrawer?.classList.add("z-40");
				burgerBtn.classList.add("burger-active");
				document.body.style.overflow = "hidden";
			}

			// Función para cerrar el drawer
			function closeDrawer() {
				drawer.classList.remove("drawer-open");
				overlay.classList.add("hidden");
				overlay.classList.remove("drawer-overlay-visible");
				divDrawer?.classList.remove("z-40");
				burgerBtn.classList.remove("burger-active");
				document.body.style.overflow = "";
			}

			// Event listener para toggle del drawer
			burgerBtn.addEventListener("click", (e) => {
				e.preventDefault();
				if (drawer.classList.contains("drawer-open")) {
					closeDrawer();
				} else {
					openDrawer();
				}
			});

			// Cerrar drawer con tecla ESC
			document.addEventListener("keydown", (e) => {
				if (
					e.key === "Escape" &&
					drawer.classList.contains("drawer-open")
				) {
					closeDrawer();
				}
			});

			// Exponer funciones globalmente para que el drawer pueda usarlas
			window.drawerControls = window.drawerControls || {};
			window.drawerControls[targetDrawer] = {
				open: openDrawer,
				close: closeDrawer,
			};
		});
	});
</script>
