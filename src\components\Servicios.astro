---
import ServiceCard from "./ServiceCard.astro";
import { ModalServicio } from "./ui/modals";
import { getServicios } from "../data/servicios";
import { generateModalId } from "../utils/modal-utils";

// Cargar los datos de los servicios desde el archivo JSON
const servicios = getServicios();
---

<section id="servicios" class="py-16 bg-gray-50">
	<div class="container mx-auto px-4">
		<div class="text-center mb-12">
			<h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
				Nuestros Servicios
			</h2>
			<p class="text-lg text-gray-600 max-w-3xl mx-auto mb-6">
				Ofrecemos una amplia gama de tratamientos podológicos para todas las
				edades y necesidades
			</p>
			<a
				href="/servicios"
				class="hidden /*inline-flex*/ items-center text-primary-600 hover:text-primary-700 font-medium transition-colors"
			>
				Ver todos los servicios
				<span class="material-symbols-outlined ml-2">arrow_forward</span>
			</a>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
			{
				servicios.map((servicio) => (
					<ServiceCard
						image={servicio.image}
						title={servicio.title}
						description={servicio.description}
						alt={servicio.alt}
						servicioId={servicio.id}
						enableModal={true}
						enablePageNavigation={false}
					/>
				))
			}
		</div>
	</div>

	<!-- Modales de servicios -->
	{
		servicios.map((servicio) => (
			<ModalServicio
				id={generateModalId("servicio", servicio.id)}
				servicio={servicio.servicio}
			/>
		))
	}
</section>
