---
import DetailLayout from '../../layouts/DetailLayout.astro';
import { getEquipo, type Miembro } from '../../data/equipo';

export async function getStaticPaths() {
	const equipo = getEquipo();
	
	return equipo.map((miembro) => ({
		params: { slug: miembro.id },
		props: { miembro },
	}));
}

interface Props {
	miembro: Miembro;
}

const { miembro } = Astro.props;

const breadcrumbs = [
	{ name: 'Equipo', href: '/#equipo' },
	{ name: miembro.name, href: `/equipo/${miembro.id}` }
];
---

<DetailLayout 
	title={miembro.miembro.nombre}
	description={miembro.miembro.descripcion}
	image={miembro.image}
	breadcrumbs={breadcrumbs}
>
	<!-- Información Principal -->
	<div class="bg-white rounded-xl shadow-lg p-8 mb-8">
		<div class="flex flex-col md:flex-row items-start md:items-center mb-6">
			<div class="mb-4 md:mb-0 md:mr-6">
				<h3 class="text-2xl font-bold text-gray-900 mb-2">
					{miembro.miembro.cargo}
				</h3>
				<div class="flex items-center text-gray-600 mb-2">
					<span class="material-symbols-outlined mr-2">work</span>
					<span>{miembro.miembro.experiencia} de experiencia</span>
				</div>
				{miembro.miembro.horarios && (
					<div class="flex items-center text-gray-600">
						<span class="material-symbols-outlined mr-2">schedule</span>
						<span>{miembro.miembro.horarios}</span>
					</div>
				)}
			</div>
		</div>
		
		<div class="prose prose-lg max-w-none">
			<p class="text-gray-700 leading-relaxed">
				{miembro.miembro.descripcion}
			</p>
		</div>
	</div>

	<!-- Especialidades -->
	{miembro.miembro.especialidades && miembro.miembro.especialidades.length > 0 && (
		<div class="bg-white rounded-xl shadow-lg p-8 mb-8">
			<h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
				<span class="material-symbols-outlined text-primary-600 mr-3">medical_services</span>
				Especialidades
			</h2>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				{miembro.miembro.especialidades.map((especialidad) => (
					<div class="flex items-center p-4 bg-primary-50 rounded-lg">
						<span class="material-symbols-outlined text-primary-600 mr-3">check_circle</span>
						<span class="text-gray-800 font-medium">{especialidad}</span>
					</div>
				))}
			</div>
		</div>
	)}

	<!-- Formación -->
	{miembro.miembro.formacion && miembro.miembro.formacion.length > 0 && (
		<div class="bg-white rounded-xl shadow-lg p-8 mb-8">
			<h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
				<span class="material-symbols-outlined text-primary-600 mr-3">school</span>
				Formación Académica
			</h2>
			<div class="space-y-4">
				{miembro.miembro.formacion.map((formacion, index) => (
					<div class="flex items-start">
						<div class="bg-primary-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1 text-sm font-bold">
							{index + 1}
						</div>
						<p class="text-gray-700 pt-1">{formacion}</p>
					</div>
				))}
			</div>
		</div>
	)}

	<!-- Certificaciones -->
	{miembro.miembro.certificaciones && miembro.miembro.certificaciones.length > 0 && (
		<div class="bg-white rounded-xl shadow-lg p-8 mb-8">
			<h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
				<span class="material-symbols-outlined text-primary-600 mr-3">verified</span>
				Certificaciones
			</h2>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				{miembro.miembro.certificaciones.map((certificacion) => (
					<div class="flex items-start p-4 bg-green-50 rounded-lg">
						<span class="material-symbols-outlined text-green-600 mr-3 mt-1">badge</span>
						<span class="text-gray-800">{certificacion}</span>
					</div>
				))}
			</div>
		</div>
	)}

	<!-- Idiomas -->
	{miembro.miembro.idiomas && miembro.miembro.idiomas.length > 0 && (
		<div class="bg-white rounded-xl shadow-lg p-8 mb-8">
			<h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
				<span class="material-symbols-outlined text-primary-600 mr-3">language</span>
				Idiomas
			</h2>
			<div class="flex flex-wrap gap-3">
				{miembro.miembro.idiomas.map((idioma) => (
					<span class="bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
						{idioma}
					</span>
				))}
			</div>
		</div>
	)}

	<!-- Información de Contacto -->
	<div class="bg-white rounded-xl shadow-lg p-8 mb-8">
		<h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
			<span class="material-symbols-outlined text-primary-600 mr-3">contact_phone</span>
			Información de Contacto
		</h2>
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			{miembro.miembro.telefono && (
				<div class="flex items-center">
					<span class="material-symbols-outlined text-primary-600 mr-3">call</span>
					<div>
						<p class="text-sm text-gray-600">Teléfono</p>
						<a href={`tel:${miembro.miembro.telefono}`} class="text-primary-600 hover:text-primary-700 font-medium">
							{miembro.miembro.telefono}
						</a>
					</div>
				</div>
			)}
			{miembro.miembro.email && (
				<div class="flex items-center">
					<span class="material-symbols-outlined text-primary-600 mr-3">email</span>
					<div>
						<p class="text-sm text-gray-600">Email</p>
						<a href={`mailto:${miembro.miembro.email}`} class="text-primary-600 hover:text-primary-700 font-medium">
							{miembro.miembro.email}
						</a>
					</div>
				</div>
			)}
		</div>
	</div>

	<!-- Call to Action -->
	<div class="bg-primary-50 rounded-xl p-8 text-center">
		<h3 class="text-2xl font-bold text-gray-900 mb-4">
			¿Quieres una consulta con {miembro.name}?
		</h3>
		<p class="text-gray-600 mb-6">
			Contacta con nosotros para reservar tu cita
		</p>
		<div class="flex flex-col sm:flex-row gap-4 justify-center">
			<a 
				href="/#contacto" 
				class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
			>
				<span class="material-symbols-outlined mr-2">calendar_today</span>
				Reservar Cita
			</a>
			{miembro.miembro.telefono && (
				<a 
					href={`tel:${miembro.miembro.telefono}`}
					class="inline-flex items-center px-6 py-3 bg-white text-primary-600 border border-primary-600 rounded-lg hover:bg-primary-50 transition-colors"
				>
					<span class="material-symbols-outlined mr-2">call</span>
					Llamar Directamente
				</a>
			)}
		</div>
	</div>
</DetailLayout>
