---
/**
 * Button02 - Componente reutilizable para botones secundarios/outline
 *
 * Este componente muestra un botón con estilo outline/borde.
 * Diseñado para ser completamente reutilizable en otros proyectos.
 *
 * @param textoBoton - Texto del botón
 * @param href - URL de destino (opcional, para enlaces)
 * @param onClick - Función onClick personalizada (opcional)
 * @param type - Tipo de botón: 'button' | 'link' (default: 'link')
 */

export interface Props {
	textoBoton: string;
	href?: string;
	onClick?: string;
	type?: "button" | "link";
	class?: string;
}

const {
	textoBoton,
	href = "#",
	onClick,
	type = "link",
	class: className = "",
} = Astro.props;
---

{
	type === "link" ? (
		<a
			href={href}
			class={`border border-primary-600 text-primary-600 hover:bg-primary-50 font-semibold px-6 py-3 rounded-lg transition-colors text-center inline-block ${className}`}
			onclick={onClick}
		>
			{textoBoton}
		</a>
	) : (
		<button
			class={`border border-primary-600 text-primary-600 hover:bg-primary-50 font-semibold px-6 py-3 rounded-lg transition-colors ${className}`}
			onclick={onClick}
		>
			{textoBoton}
		</button>
	)
}
