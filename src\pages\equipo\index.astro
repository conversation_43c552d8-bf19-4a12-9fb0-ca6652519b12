---
import Layout from '../../layouts/Layout.astro';
import EquipoCard from '../../components/EquipoCard.astro';
import { getEquipo } from '../../data/equipo';

const equipo = getEquipo();
---

<Layout title="Equipo - Clínica Podológica" description="Conoce a nuestro equipo de profesionales especializados en podología">
	<main class="min-h-screen bg-gray-50">
		<!-- Breadcrumbs -->
		<nav class="bg-white border-b border-gray-200">
			<div class="container mx-auto px-4 py-3">
				<ol class="flex items-center space-x-2 text-sm text-gray-600">
					<li>
						<a href="/" class="hover:text-primary-600 transition-colors">
							<span class="material-symbols-outlined text-base">home</span>
						</a>
					</li>
					<li class="flex items-center">
						<span class="material-symbols-outlined text-gray-400 mx-2">chevron_right</span>
						<span class="text-gray-900 font-medium">Equipo</span>
					</li>
				</ol>
			</div>
		</nav>

		<!-- Hero Section -->
		<section class="bg-white py-16">
			<div class="container mx-auto px-4">
				<div class="text-center mb-12">
					<h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
						Nuestro Equipo
					</h1>
					<p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
						Conoce a los profesionales que cuidan de tu salud podológica 
						con dedicación y experiencia
					</p>
				</div>
			</div>
		</section>

		<!-- Equipo Grid -->
		<section class="py-16">
			<div class="container mx-auto px-4">
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
					{
						equipo.map((miembro) => (
							<EquipoCard
								image={miembro.image}
								name={miembro.name}
								position={miembro.position}
								description={miembro.description}
								alt={miembro.alt}
								miembroId={miembro.id}
								enableModal={false}
								enablePageNavigation={true}
							/>
						))
					}
				</div>
			</div>
		</section>

		<!-- Call to Action -->
		<section class="py-16 bg-primary-50">
			<div class="container mx-auto px-4">
				<div class="text-center">
					<h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
						¿Quieres conocer más sobre nuestro equipo?
					</h2>
					<p class="text-gray-600 mb-8 max-w-2xl mx-auto">
						Nuestros profesionales están aquí para ofrecerte el mejor cuidado. 
						Contacta con nosotros para más información.
					</p>
					<div class="flex flex-col sm:flex-row gap-4 justify-center">
						<a 
							href="/#contacto" 
							class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
						>
							<span class="material-symbols-outlined mr-2">contact_mail</span>
							Contactar
						</a>
						<a 
							href="/" 
							class="inline-flex items-center px-6 py-3 bg-white text-primary-600 border border-primary-600 rounded-lg hover:bg-primary-50 transition-colors"
						>
							<span class="material-symbols-outlined mr-2">home</span>
							Volver al Inicio
						</a>
					</div>
				</div>
			</div>
		</section>
	</main>
</Layout>
