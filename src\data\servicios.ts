/**
 * Tipos y utilidades para el sistema de servicios reutilizable
 * 
 * Este módulo proporciona:
 * - Tipos TypeScript para la estructura de servicios
 * - Función para cargar servicios desde JSON
 * - Validación de estructura de datos
 */

// Tipo para la información detallada del servicio (modal)
export interface ServicioDetalle {
  titulo: string;
  descripcion: string;
  icono: string;
  precio?: string;
  duracion?: string;
  beneficios?: string[];
  proceso?: string[];
  imagenes?: string[];
}

// Tipo para el servicio completo (tarjeta + modal)
export interface Servicio {
  id: string;
  image: string;
  title: string;
  description: string;
  alt: string;
  servicio: ServicioDetalle;
}

// Tipo para la estructura completa del archivo JSON
export interface ServiciosData {
  _comment?: string;
  _format?: any;
  _default_example?: any;
  servicios: Servicio[];
}

// Importar los datos desde el archivo JSON
import serviciosData from './servicios.json';
import { getConfig } from './config';

/**
 * Obtiene todos los servicios desde el archivo JSON
 * @returns Array de servicios
 */
export function getServicios(): Servicio[] {
  const data = serviciosData as ServiciosData;
  return data.servicios || [];
}

/**
 * Obtiene un servicio específico por su ID
 * @param id - ID del servicio a buscar
 * @returns El servicio encontrado o undefined
 */
export function getServicioById(id: string): Servicio | undefined {
  const servicios = getServicios();
  return servicios.find(servicio => servicio.id === id);
}

/**
 * Valida que un servicio tenga la estructura correcta
 * @param servicio - Objeto a validar
 * @returns true si la estructura es válida
 */
export function validarServicio(servicio: any): servicio is Servicio {
  const config = getConfig();

  if (!config.validacion.validarEstructura) {
    return true; // Saltar validación si está deshabilitada
  }

  const esValido = (
    typeof servicio === 'object' &&
    typeof servicio.id === 'string' &&
    typeof servicio.image === 'string' &&
    typeof servicio.title === 'string' &&
    typeof servicio.description === 'string' &&
    typeof servicio.alt === 'string' &&
    typeof servicio.servicio === 'object' &&
    typeof servicio.servicio.titulo === 'string' &&
    typeof servicio.servicio.descripcion === 'string' &&
    typeof servicio.servicio.icono === 'string'
  );

  if (!esValido && config.validacion.mostrarErroresConsola) {
    console.error('Servicio con estructura inválida:', servicio);
  }

  return esValido;
}

/**
 * Valida que todos los servicios tengan la estructura correcta
 * @param servicios - Array de servicios a validar
 * @returns true si todos los servicios son válidos
 */
export function validarServicios(servicios: any[]): servicios is Servicio[] {
  return Array.isArray(servicios) && servicios.every(validarServicio);
}

// Exportar los servicios por defecto
export const servicios = getServicios();
export default servicios;
