# Ejemplos Prácticos - Sistema de Páginas Dinámicas

## 🎯 Casos de Uso Comunes

Esta guía presenta ejemplos prácticos de cómo usar y personalizar el sistema de páginas dinámicas.

## 📝 Ejemplo 1: Agregar Nuevo Servicio

### Escenario
Quieres agregar un nuevo servicio de "Podología Infantil" a tu clínica.

### Paso a Paso

1. **Edita** `src/data/servicios.json`:

```json
{
  "id": "podologia-infantil",
  "image": "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3",
  "title": "Podología Infantil",
  "description": "Cuidado especializado de los pies de los más pequeños",
  "alt": "Podología infantil - cuidado de pies de niños",
  "servicio": {
    "titulo": "Podología Infantil Especializada",
    "descripcion": "Ofrecemos cuidado podológico especializado para niños desde los primeros pasos hasta la adolescencia. Nuestro enfoque lúdico y profesional garantiza una experiencia positiva para los pequeños pacientes.",
    "icono": "child_care",
    "precio": "Desde 25€",
    "duracion": "30-45 minutos",
    "beneficios": [
      "Detección temprana de problemas",
      "Corrección de alteraciones de la marcha",
      "Prevención de deformidades",
      "Tratamiento de verrugas plantares",
      "Educación en higiene podal",
      "Ambiente adaptado para niños"
    ],
    "proceso": [
      "Evaluación lúdica del pie y la marcha",
      "Exploración adaptada a la edad del niño",
      "Tratamiento suave y sin dolor",
      "Educación para padres e hijos",
      "Seguimiento del desarrollo podal",
      "Recomendaciones de calzado apropiado"
    ],
    "imagenes": [
      "/images/servicios/podologia-infantil-1.jpg",
      "/images/servicios/podologia-infantil-2.jpg"
    ]
  }
}
```

2. **Resultado**: Automáticamente se genera:
   - Página: `/servicios/podologia-infantil`
   - Tarjeta en `/servicios`
   - Tarjeta en la página principal (sección servicios)

## 👥 Ejemplo 2: Agregar Nuevo Miembro del Equipo

### Escenario
Incorporas una nueva podóloga especialista en biomecánica.

### Implementación

```json
{
  "id": "ana-martinez",
  "image": "/images/equipo/ana-martinez.jpg",
  "name": "Ana Martínez",
  "position": "Especialista en Biomecánica",
  "description": "Experta en análisis de la marcha y plantillas personalizadas",
  "alt": "Ana Martínez - Especialista en Biomecánica",
  "miembro": {
    "nombre": "Ana Martínez López",
    "cargo": "Podóloga Especialista en Biomecánica y Ortopodología",
    "descripcion": "Con más de 12 años de experiencia en biomecánica del pie, Ana se especializa en el análisis computerizado de la marcha y la fabricación de plantillas ortopédicas personalizadas. Su enfoque integral combina tecnología avanzada con un trato cercano y profesional.",
    "experiencia": "12+ años",
    "especialidades": [
      "Análisis biomecánico computerizado",
      "Plantillas ortopédicas personalizadas",
      "Podología deportiva",
      "Alteraciones de la marcha",
      "Pie plano y cavo",
      "Fascitis plantar"
    ],
    "formacion": [
      "Diplomada en Podología - Universidad Complutense Madrid",
      "Máster en Biomecánica Clínica - Universidad de Barcelona",
      "Especialización en Ortopodología - Instituto Europeo del Pie",
      "Curso Avanzado en Análisis de la Marcha - AECP"
    ],
    "certificaciones": [
      "Colegio Profesional de Podólogos de Madrid",
      "Certificación en Análisis Biomecánico 3D",
      "Especialista en Plantillas CAD-CAM",
      "Formación en Podología Deportiva Avanzada"
    ],
    "idiomas": [
      "Español (nativo)",
      "Inglés (avanzado)",
      "Francés (intermedio)"
    ],
    "horarios": "Martes a Sábado: 9:00 - 14:00 y 16:00 - 19:00",
    "telefono": "+34 654 321 987",
    "email": "<EMAIL>",
    "linkedin": "https://linkedin.com/in/ana-martinez-podologa",
    "imagenes": [
      "/images/equipo/ana-martinez-consulta.jpg",
      "/images/equipo/ana-martinez-biomecánica.jpg"
    ]
  }
}
```

### URLs Generadas
- `/equipo/ana-martinez` - Perfil completo
- Aparece en `/equipo` - Lista del equipo
- Tarjeta en página principal

## 🏗️ Ejemplo 3: Proyecto Complejo

### Escenario
Documentar un proyecto de modernización de clínica con múltiples fases.

```json
{
  "id": "modernizacion-clinica-valencia",
  "title": "Modernización Integral Valencia",
  "description": "Transformación completa de clínica podológica con tecnología de vanguardia",
  "image": "/images/proyectos/valencia-exterior.jpg",
  "alt": "Clínica Valencia modernizada",
  "proyecto": {
    "titulo": "Modernización Integral Clínica Podológica Valencia",
    "descripcion": "Proyecto ambicioso de transformación completa de una clínica podológica tradicional en un centro de referencia tecnológica. El proyecto abarcó renovación arquitectónica, implementación de tecnología avanzada, formación del personal y optimización de procesos.",
    "cliente": "Podología Avanzada Valencia S.L.",
    "fecha": "Septiembre 2024 - Enero 2025",
    "ubicacion": "Valencia, España",
    "estado": "en-progreso",
    "presupuesto": "€180,000",
    "duracion": "5 meses",
    "tecnologias": [
      "Sistema de análisis biomecánico 3D",
      "Láser terapéutico de última generación",
      "Software de gestión integral",
      "Equipos de esterilización avanzada",
      "Sistema de citas online",
      "Plataforma de telemedicina"
    ],
    "caracteristicas": [
      "Renovación completa de 6 consultas especializadas",
      "Laboratorio de biomecánica con tecnología 3D",
      "Sala de espera inteligente con sistema de turnos",
      "Zona de rehabilitación podológica",
      "Área de fabricación de plantillas in-situ",
      "Sistema de climatización y purificación de aire",
      "Accesibilidad universal mejorada",
      "Parking privado para pacientes"
    ],
    "equipo": [
      "Arquitecto especializado en centros sanitarios",
      "Ingeniero biomédico",
      "Diseñador de interiores",
      "Especialista en tecnología médica",
      "Project Manager certificado",
      "Consultor en procesos clínicos"
    ],
    "resultados": [
      "Aumento del 65% en la capacidad de atención",
      "Reducción del 40% en tiempos de espera",
      "Mejora del 80% en satisfacción del paciente",
      "Incremento del 50% en precisión diagnóstica",
      "Reducción del 30% en costes operativos",
      "Certificación ISO 9001 obtenida"
    ],
    "imagenes": [
      "/images/proyectos/valencia-antes.jpg",
      "/images/proyectos/valencia-durante.jpg",
      "/images/proyectos/valencia-consulta-1.jpg",
      "/images/proyectos/valencia-laboratorio.jpg",
      "/images/proyectos/valencia-recepcion.jpg",
      "/images/proyectos/valencia-equipos.jpg"
    ]
  }
}
```

## 🎨 Ejemplo 4: Personalización de Componentes

### Escenario
Quieres mostrar solo páginas (sin modales) en la sección de servicios.

```astro
---
// src/components/Servicios.astro (modificado)
import ServiceCard from "./ServiceCard.astro";
import { getServicios } from "../data/servicios";

const servicios = getServicios();
---

<section id="servicios" class="py-16 bg-gray-50">
  <div class="container mx-auto px-4">
    <!-- ... header ... -->
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {
        servicios.map((servicio) => (
          <ServiceCard
            image={servicio.image}
            title={servicio.title}
            description={servicio.description}
            alt={servicio.alt}
            servicioId={servicio.id}
            enableModal={false}        // ❌ Sin modales
            enablePageNavigation={true} // ✅ Solo páginas
          />
        ))
      }
    </div>
  </div>
</section>
```

## 🔧 Ejemplo 5: Función Personalizada

### Escenario
Necesitas obtener solo servicios con precio específico.

```typescript
// src/data/servicios.ts (agregar función)
export function getServiciosPorRangoPrecio(min: number, max: number): Servicio[] {
  const servicios = getServicios();
  
  return servicios.filter(servicio => {
    const precio = servicio.servicio.precio;
    if (!precio) return false;
    
    // Extraer número del precio (ej: "Desde 35€" -> 35)
    const match = precio.match(/(\d+)/);
    if (!match) return false;
    
    const precioNumerico = parseInt(match[1]);
    return precioNumerico >= min && precioNumerico <= max;
  });
}

// Uso en componente
const serviciosEconomicos = getServiciosPorRangoPrecio(0, 50);
```

## 📱 Ejemplo 6: Página Personalizada

### Escenario
Crear una página especial para servicios premium.

```astro
---
// src/pages/servicios-premium.astro
import Layout from '../layouts/Layout.astro';
import ServiceCard from '../components/ServiceCard.astro';
import { getServicios } from '../data/servicios';

// Filtrar servicios premium (precio > 80€)
const servicios = getServicios();
const serviciosPremium = servicios.filter(servicio => {
  const precio = servicio.servicio.precio;
  if (!precio) return false;
  const match = precio.match(/(\d+)/);
  return match && parseInt(match[1]) > 80;
});
---

<Layout title="Servicios Premium" description="Nuestros tratamientos más avanzados">
  <main class="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100">
    <section class="py-20">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Servicios Premium
          </h1>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Tratamientos avanzados con la tecnología más moderna
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {
            serviciosPremium.map((servicio) => (
              <ServiceCard
                image={servicio.image}
                title={servicio.title}
                description={servicio.description}
                alt={servicio.alt}
                servicioId={servicio.id}
                enableModal={false}
                enablePageNavigation={true}
              />
            ))
          }
        </div>
      </div>
    </section>
  </main>
</Layout>
```

## 🎯 Ejemplo 7: Integración con Formularios

### Escenario
Agregar formulario de contacto específico en páginas de servicios.

```astro
---
// En src/pages/servicios/[slug].astro (modificar CTA)
---

<!-- Call to Action con formulario -->
<div class="bg-primary-50 rounded-xl p-8">
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
    <div>
      <h3 class="text-2xl font-bold text-gray-900 mb-4">
        Reserva tu cita para {servicio.title}
      </h3>
      <p class="text-gray-600 mb-6">
        Completa el formulario y te contactaremos en menos de 24 horas
      </p>
    </div>
    
    <form class="bg-white p-6 rounded-lg shadow-md">
      <input type="hidden" name="servicio" value={servicio.id} />
      
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Nombre completo
        </label>
        <input 
          type="text" 
          name="nombre" 
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
        />
      </div>
      
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Teléfono
        </label>
        <input 
          type="tel" 
          name="telefono" 
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
        />
      </div>
      
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Mensaje (opcional)
        </label>
        <textarea 
          name="mensaje" 
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
        ></textarea>
      </div>
      
      <button 
        type="submit"
        class="w-full bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 transition-colors"
      >
        Solicitar Cita
      </button>
    </form>
  </div>
</div>
```

---

Estos ejemplos muestran la flexibilidad del sistema para adaptarse a diferentes necesidades y casos de uso específicos.
