---
/**
 * ModalServicio - Componente reutilizable para mostrar detalles de servicios
 *
 * Este componente está diseñado para ser completamente reutilizable en otros proyectos.
 * Los datos se cargan desde archivos JSON externos, lo que permite:
 * - Fácil mantenimiento de contenido
 * - Reutilización en múltiples proyectos
 * - Separación entre lógica y datos
 *
 * Para usar en otro proyecto:
 * 1. Copia este archivo y ModalBase.astro
 * 2. Adapta los estilos CSS según tu diseño
 * 3. Carga los datos desde tu propio archivo JSON
 * 4. Mantén la estructura de la interfaz Props para compatibilidad
 */
import ModalBase from "./ModalBase.astro";

export interface Props {
	id: string;
	servicio: {
		titulo: string;
		descripcion: string;
		icono: string;
		precio?: string;
		duracion?: string;
		beneficios?: string[];
		proceso?: string[];
		imagenes?: string[];
	};
}

const { id, servicio } = Astro.props;
---

<ModalBase id={id} title={servicio.titulo} size="xl" topOffset="120px" overlayType="blur">
	<div class="space-y-6">
		<!-- Descripción principal -->
		<div class="text-gray-700 leading-relaxed">
			<p>{servicio.descripcion}</p>
		</div>

		<!-- Info básica -->
		<!-- {
			(servicio.precio || servicio.duracion) && (
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					{servicio.precio && (
						<div class="bg-primary-50 rounded-xl p-4">
							<div class="flex items-center">
								<span class="material-symbols-outlined text-primary-600 mr-2">
									payments
								</span>
								<div>
									<p class="text-sm text-gray-600">Precio</p>
									<p class="font-semibold text-gray-800">
										{servicio.precio}
									</p>
								</div>
							</div>
						</div>
					)}

					{servicio.duracion && (
						<div class="bg-blue-50 rounded-xl p-4">
							<div class="flex items-center">
								<span class="material-symbols-outlined text-blue-600 mr-2">
									schedule
								</span>
								<div>
									<p class="text-sm text-gray-600">Duración</p>
									<p class="font-semibold text-gray-800">
										{servicio.duracion}
									</p>
								</div>
							</div>
						</div>
					)}
				</div>
			)
		} -->

		<!-- Beneficios -->
		{
			servicio.beneficios && servicio.beneficios.length > 0 && (
				<div
					class={
						servicio.imagenes && servicio.imagenes.length >= 1
							? "grid grid-cols-1 md:grid-cols-2 gap-6 items-start"
							: ""
					}
				>
					<!-- Lista de beneficios -->
					<div>
						<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
							<span class="material-symbols-outlined text-green-600 mr-2">
								check_circle
							</span>
							Beneficios
						</h3>
						<ul class="space-y-2">
							{servicio.beneficios.map((beneficio) => (
								<li class="flex items-start">
									<span class="material-symbols-outlined text-green-500 text-xl mr-2 mt-0.5">
										check
									</span>
									<span class="text-gray-700">{beneficio}</span>
								</li>
							))}
						</ul>
					</div>

					<!-- Imagen si existe -->
					{servicio.imagenes && servicio.imagenes.length >= 1 && (
						<div class="overflow-hidden rounded-xl shadow-lg">
							<img
								src={servicio.imagenes[0]}
								alt={servicio.titulo + " beneficios"}
								class="w-full h-full object-cover transition-transform hover:scale-105 duration-500"
							/>
						</div>
					)}
				</div>
			)
		}

		<!-- Proceso -->
		{
			servicio.proceso && servicio.proceso.length > 0 && (
				<div
					class={
						(servicio.imagenes && servicio.imagenes.length >= 2
							? "grid grid-cols-1 md:grid-cols-2 gap-6 items-start"
							: "") + " hidden"
					}
				>
					<!-- Lista de proceso -->
					<div>
						<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
							<span class="material-symbols-outlined text-blue-600 mr-2">
								list
							</span>
							¿Cómo funciona?
						</h3>
						<ol class="space-y-3">
							{servicio.proceso.map((paso, index) => (
								<li class="flex items-start">
									<div class="bg-primary-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold mr-3 mt-0.5 flex-shrink-0">
										{index + 1}
									</div>
									<span class="text-gray-700">{paso}</span>
								</li>
							))}
						</ol>
					</div>

					<!-- Segunda imagen si existe -->
					{servicio.imagenes && servicio.imagenes.length >= 2 && (
						<div class="overflow-hidden rounded-xl shadow-lg">
							<img
								src={servicio.imagenes[1]}
								alt={servicio.titulo + " proceso"}
								class="w-full h-full object-cover transition-transform hover:scale-105 duration-500"
							/>
						</div>
					)}
				</div>
			)
		}


		<!-- Galería de imágenes -->
		{
			servicio.imagenes && servicio.imagenes.length > 0 && (
				<div class="hidden">
					<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
						<span class="material-symbols-outlined text-purple-600 mr-2">
							photo_library
						</span>
						Galería
					</h3>
					<div class="grid grid-cols-2 md:grid-cols-3 gap-3">
						{servicio.imagenes.map((imagen, index) => (
							<img
								src={imagen}
								alt={`${servicio.titulo} ${index + 1}`}
								class="rounded-lg object-cover h-24 w-full hover:scale-105 transition-transform cursor-pointer"
							/>
						))}
					</div>
				</div>
			)
		}

		<!-- CTA -->
		<div
			class="bg-gradient-to-r from-primary-50 to-blue-50 rounded-xl p-6 text-center"
		>
			<p class="text-gray-700 mb-4">¿Interesado en este servicio?</p>
			<button
				class="bg-primary-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary-700 transition-colors shadow-lg hover:shadow-xl"
				data-modal-target="citaModal"
				data-modal-close
			>
				Pedir cita ahora
			</button>
		</div>
	</div>
</ModalBase>
