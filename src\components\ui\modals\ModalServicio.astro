---
/**
 * ModalServicio - Componente reutilizable para mostrar detalles de servicios
 *
 * Este componente está diseñado para ser completamente reutilizable en otros proyectos.
 * Los datos se cargan desde archivos JSON externos, lo que permite:
 * - Fácil mantenimiento de contenido
 * - Reutilización en múltiples proyectos
 * - Separación entre lógica y datos
 *
 * Para usar en otro proyecto:
 * 1. Copia este archivo y ModalBase.astro
 * 2. Adapta los estilos CSS según tu diseño
 * 3. Carga los datos desde tu propio archivo JSON
 * 4. Mantén la estructura de la interfaz Props para compatibilidad
 */
import ModalBase from "./ModalBase.astro";
import ImageGallery from "../gallery/ImageGallery.astro";
import Button01 from "../buttons/Button01.astro";
import ItemList from "../lists/ItemList.astro";

export interface Props {
	id: string;
	servicio: {
		titulo: string;
		descripcion: string;
		icono: string;
		precio?: string;
		duracion?: string;
		beneficios?: string[];
		proceso?: string[];
		imagenes?: string[];
	};
}

const { id, servicio } = Astro.props;
---

<ModalBase
	id={id}
	title={servicio.titulo}
	size="xl"
	topOffset="120px"
	overlayType="blur"
>
	<div class="space-y-6">
		<!-- Descripción principal -->
		<div class="text-gray-700 leading-relaxed">
			<p>{servicio.descripcion}</p>
		</div>

		<!-- Info básica -->
		<!-- {
			(servicio.precio || servicio.duracion) && (
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					{servicio.precio && (
						<div class="bg-primary-50 rounded-xl p-4">
							<div class="flex items-center">
								<span class="material-symbols-outlined text-primary-600 mr-2">
									payments
								</span>
								<div>
									<p class="text-sm text-gray-600">Precio</p>
									<p class="font-semibold text-gray-800">
										{servicio.precio}
									</p>
								</div>
							</div>
						</div>
					)}

					{servicio.duracion && (
						<div class="bg-blue-50 rounded-xl p-4">
							<div class="flex items-center">
								<span class="material-symbols-outlined text-blue-600 mr-2">
									schedule
								</span>
								<div>
									<p class="text-sm text-gray-600">Duración</p>
									<p class="font-semibold text-gray-800">
										{servicio.duracion}
									</p>
								</div>
							</div>
						</div>
					)}
				</div>
			)
		} -->

		<!-- Beneficios -->
		<ItemList
			items={servicio.beneficios || []}
			titulo="Beneficios"
			titleIcon="check_circle"
			titleIconColor="text-green-600"
			icon="check"
			iconColor="text-green-500"
			imagen={servicio.imagenes && servicio.imagenes.length >= 1
				? servicio.imagenes[0]
				: undefined}
			imagenAlt={`${servicio.titulo} beneficios`}
		/>

		<!-- Proceso -->
		<ItemList
			items={servicio.proceso || []}
			titulo="¿Cómo funciona?"
			titleIcon="list"
			titleIconColor="text-blue-600"
			listType="ordered"
			imagen={servicio.imagenes && servicio.imagenes.length >= 2
				? servicio.imagenes[1]
				: undefined}
			imagenAlt={`${servicio.titulo} proceso`}
		/>

		<!-- Galería de imágenes -->
		<ImageGallery
			imagenes={servicio.imagenes || []}
			titulo={servicio.titulo}
			topOffset="80px"
		/>

		<!-- CTA -->
		<div
			class="bg-gradient-to-r from-primary-50 to-blue-50 rounded-xl p-6 text-center"
		>
			<p class="text-gray-700 mb-4">¿Interesado en este servicio?</p>
			<Button01
				textoBoton="Pedir cita ahora"
				modalTarget="citaModal"
				closeModal={true}
			/>
		</div>
	</div>
</ModalBase>
