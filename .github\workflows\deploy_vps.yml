name: Deploy Astro to VPS

on:
   push:
      branches: ["deployment_vps"]

jobs:
   build-and-deploy:
      runs-on: ubuntu-latest

      steps:
         - name: Checkout repo
           uses: actions/checkout@v4

         - name: Setup Node.js
           uses: actions/setup-node@v4
           with:
              node-version: 20

         - name: Install dependencies
           run: npm ci

         - name: Build Astro site
           run: npm run build

         - name: Install SSH Key
           uses: shimataro/ssh-key-action@v2
           with:
              key: ${{ secrets.SSH_PRIVATE_SERVER_KEY }}
              name: vps-ssh-key
              config: |
                 Host target
                   Hostname ${{ secrets.SSH_HOST }}
                   Port ${{ secrets.SSH_PORT }}
                   User ${{ secrets.SSH_USER }}
                   IdentityFile ~/.ssh/vps-ssh-key
                   StrictHostKeyChecking yes
              known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}
         - name: Deploy to VPS
           run: |
              scp -r dist/* target:${{ secrets.SSH_SERVER_DESTINATION }}
              ssh target "chown -R ${{ secrets.SSH_USER }}:www-data ${{ secrets.SSH_SERVER_DESTINATION }}"
