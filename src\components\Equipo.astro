---
import EquipoCard from "./EquipoCard.astro";
import { getEquipo } from "../data/equipo";

// Cargar los datos del equipo desde el archivo JSON
const equipo = getEquipo();
---

<section id="equipo" class="py-16">
	<div class="container mx-auto px-4">
		<div class="text-center mb-12">
			<h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
				Nuestro Equipo Profesional
			</h2>
			<p class="text-lg text-gray-600 max-w-3xl mx-auto mb-6">
				Conoce a los profesionales que cuidan de tu salud podológica con
				dedicación y experiencia
			</p>
			<a
				href="/equipo"
				class="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium transition-colors"
			>
				Ver todo el equipo
				<span class="material-symbols-outlined ml-2">arrow_forward</span>
			</a>
		</div>

		<!-- Grid de miembros del equipo -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
			{
				equipo.map((miembro) => (
					<EquipoCard
						image={miembro.image}
						name={miembro.name}
						position={miembro.position}
						description={miembro.description}
						alt={miembro.alt}
						miembroId={miembro.id}
						enablePageNavigation={true}
						enableModal={false}
					/>
				))
			}
		</div>
	</div>
</section>
