---
export interface Props {
	id: string;
	title?: string;
	size?: "sm" | "md" | "lg" | "xl" | "full";
	closeOnBackdrop?: boolean;
	topOffset?: string; // Offset desde arriba (ej: "80px", "5rem")
	maxHeight?: string; // Altura máxima personalizada (ej: "80vh", "600px")
	overlayType?: "transparent" | "blur" | "opaque" | "custom"; // Nuevo parámetro
	customOverlayClass?: string; // Para overlay personalizado
	customOverlayStyle?: string; // Para estilos inline personalizados
}

const {
	id,
	title,
	size = "md",
	closeOnBackdrop = true,
	topOffset,
	maxHeight,
	overlayType = "transparent",
	customOverlayClass = "",
	customOverlayStyle = "",
} = Astro.props;

const sizeClasses = {
	sm: "max-w-sm",
	md: "max-w-md",
	lg: "max-w-lg",
	xl: "max-w-2xl",
	full: "w-full max-w-none",
};

// Clases para diferentes tipos de overlay
const overlayClasses = {
	transparent: "bg-black/90",
	blur: "bg-black/30 backdrop-blur-md",
	opaque: "bg-black",
	custom: customOverlayClass,
};

// Calcular estilos dinámicos
const modalStyles = topOffset
	? `padding-top: ${topOffset}; align-items: flex-start;`
	: "";

const containerMaxHeight = maxHeight
	? maxHeight
	: topOffset
		? `calc(100vh - ${topOffset} - 2rem)` // 2rem para padding inferior
		: "90vh";

const contentMaxHeight = maxHeight
	? `calc(${maxHeight} - 120px)`
	: topOffset
		? `calc(100vh - ${topOffset} - 2rem - 120px)` // 120px para header
		: "calc(90vh - 120px)";

// Estilos del overlay
const overlayStyle =
	overlayType === "custom" && customOverlayStyle ? customOverlayStyle : "";
---

<!-- Modal Base -->
<div
	id={id}
	class="fixed inset-0 z-50 hidden items-center justify-center p-4"
	style={modalStyles}
>
	<!-- Fondo/Overlay -->
	<div
		class={`absolute inset-0 transition-opacity ${overlayClasses[overlayType]}`}
		style={overlayStyle}
		data-modal-close={closeOnBackdrop ? "" : undefined}
	>
	</div>

	<!-- Contenedor del modal -->
	<div
		class={`relative bg-white rounded-2xl shadow-xl w-full ${sizeClasses[size]} overflow-hidden z-10 animate-fadeIn`}
		style={`max-height: ${containerMaxHeight};`}
	>
		<!-- Header -->
		<div
			class="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0"
		>
			{
				title && (
					<h2 class="text-2xl font-header text-primary-600">{title}</h2>
				)
			}
			<button
				class="ml-auto text-gray-400 hover:text-gray-600 transition-colors"
				data-modal-close
				aria-label="Cerrar modal"
			>
				<span class="material-symbols-outlined text-2xl">close</span>
			</button>
		</div>

		<!-- Contenido -->
		<div
			class="p-6 overflow-y-auto"
			style={`max-height: ${contentMaxHeight};`}
		>
			<slot />
		</div>
	</div>
</div>
