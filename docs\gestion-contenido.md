# Gestión de Contenido - Sistema de Páginas Dinámicas

## 📝 Guía para Editores de Contenido

Esta guía está dirigida a personas que necesitan agregar, modificar o eliminar contenido sin conocimientos técnicos profundos.

## 🎯 Conceptos Básicos

### ¿Qué son los archivos JSON?
Los archivos JSON son documentos de texto que contienen datos estructurados. Son fáciles de leer y editar, y el sistema los usa para generar automáticamente las páginas web.

### Ubicación de los Archivos
- **Servicios**: `src/data/servicios.json`
- **Equipo**: `src/data/equipo.json`  
- **Proyectos**: `src/data/proyectos.json`

## ✏️ Editar Contenido Existente

### Modificar un Servicio

1. **Abre** el archivo `src/data/servicios.json`
2. **Busca** el servicio que quieres modificar por su `id`
3. **Modifica** los campos necesarios
4. **Guarda** el archivo

**Ejemplo - Cambiar precio de quiropodología**:
```json
{
  "id": "quiropodologia",
  "servicio": {
    "precio": "Desde 40€",  // ← Cambiar aquí
    "duracion": "50 minutos" // ← O aquí
  }
}
```

### Modificar Información del Equipo

**Ejemplo - Actualizar horarios de Diego**:
```json
{
  "id": "diego-mateos",
  "miembro": {
    "horarios": "Lunes a Viernes: 8:00 - 15:00", // ← Nuevo horario
    "telefono": "+34 123 456 789"                 // ← Nuevo teléfono
  }
}
```

### Actualizar Estado de Proyecto

**Ejemplo - Marcar proyecto como completado**:
```json
{
  "id": "clinica-madrid",
  "proyecto": {
    "estado": "completado",  // ← Cambiar de "en-progreso" a "completado"
    "fecha": "Enero 2025"    // ← Actualizar fecha
  }
}
```

## ➕ Agregar Nuevo Contenido

### Agregar Nuevo Servicio

1. **Abre** `src/data/servicios.json`
2. **Ve** al final del array `servicios`
3. **Agrega** una coma después del último servicio
4. **Copia** esta plantilla y personalízala:

```json
{
  "id": "tu-nuevo-servicio",
  "image": "https://ejemplo.com/imagen.jpg",
  "title": "Nombre del Servicio",
  "description": "Descripción breve para la tarjeta",
  "alt": "Descripción de la imagen",
  "servicio": {
    "titulo": "Nombre Completo del Servicio",
    "descripcion": "Descripción detallada que aparecerá en la página completa",
    "icono": "medical_services",
    "precio": "Desde 35€",
    "duracion": "45 minutos",
    "beneficios": [
      "Primer beneficio",
      "Segundo beneficio",
      "Tercer beneficio"
    ],
    "proceso": [
      "Primer paso del proceso",
      "Segundo paso",
      "Tercer paso"
    ],
    "imagenes": [
      "/images/servicio-imagen1.jpg",
      "/images/servicio-imagen2.jpg"
    ]
  }
}
```

### Agregar Nuevo Miembro del Equipo

**Plantilla completa**:
```json
{
  "id": "nombre-apellido",
  "image": "/images/equipo/foto.jpg",
  "name": "Nombre Apellido",
  "position": "Cargo Principal",
  "description": "Descripción breve para la tarjeta",
  "alt": "Nombre Apellido - Cargo",
  "miembro": {
    "nombre": "Nombre Completo",
    "cargo": "Cargo Detallado",
    "descripcion": "Descripción completa del profesional",
    "experiencia": "X+ años",
    "especialidades": [
      "Especialidad 1",
      "Especialidad 2"
    ],
    "formacion": [
      "Título universitario",
      "Máster o especialización"
    ],
    "certificaciones": [
      "Certificación 1",
      "Certificación 2"
    ],
    "idiomas": [
      "Español (nativo)",
      "Inglés (intermedio)"
    ],
    "horarios": "Lunes a Viernes: 9:00 - 17:00",
    "telefono": "+34 XXX XXX XXX",
    "email": "<EMAIL>"
  }
}
```

### Agregar Nuevo Proyecto

**Plantilla completa**:
```json
{
  "id": "nombre-proyecto",
  "title": "Título del Proyecto",
  "description": "Descripción breve del proyecto",
  "image": "/images/proyectos/proyecto.jpg",
  "alt": "Descripción de la imagen",
  "proyecto": {
    "titulo": "Título Completo del Proyecto",
    "descripcion": "Descripción detallada del proyecto",
    "cliente": "Nombre del Cliente",
    "fecha": "Mes Año",
    "ubicacion": "Ciudad, País",
    "estado": "completado",
    "presupuesto": "€XX,XXX",
    "duracion": "X meses",
    "tecnologias": [
      "Tecnología 1",
      "Tecnología 2"
    ],
    "caracteristicas": [
      "Característica 1",
      "Característica 2"
    ],
    "equipo": [
      "Rol 1",
      "Rol 2"
    ],
    "resultados": [
      "Resultado 1",
      "Resultado 2"
    ],
    "imagenes": [
      "/images/proyectos/imagen1.jpg",
      "/images/proyectos/imagen2.jpg"
    ]
  }
}
```

## 🗑️ Eliminar Contenido

### Eliminar un Elemento

1. **Abre** el archivo correspondiente
2. **Busca** el elemento por su `id`
3. **Selecciona** todo el objeto (desde `{` hasta `}`)
4. **Elimina** el objeto completo
5. **Revisa** que no queden comas extra

**⚠️ Importante**: Al eliminar un elemento, asegúrate de que:
- No queden comas colgando (`,` al final sin elemento siguiente)
- La estructura JSON siga siendo válida

## 📋 Campos Obligatorios vs Opcionales

### Servicios
**Obligatorios**:
- `id`, `image`, `title`, `description`, `alt`
- `servicio.titulo`, `servicio.descripcion`, `servicio.icono`

**Opcionales**:
- `servicio.precio`, `servicio.duracion`, `servicio.beneficios`, `servicio.proceso`, `servicio.imagenes`

### Equipo
**Obligatorios**:
- `id`, `image`, `name`, `position`, `description`, `alt`
- `miembro.nombre`, `miembro.cargo`, `miembro.descripcion`, `miembro.experiencia`

**Opcionales**:
- Todos los demás campos del `miembro`

### Proyectos
**Obligatorios**:
- `id`, `title`, `description`, `image`, `alt`
- `proyecto.titulo`, `proyecto.descripcion`

**Opcionales**:
- Todos los demás campos del `proyecto`

## 🎨 Consejos de Contenido

### Imágenes
- **Tamaño recomendado**: 1200x800 píxeles
- **Formato**: JPG o PNG
- **Peso**: Máximo 500KB por imagen
- **Ubicación**: Carpeta `/public/images/`

### Textos
- **Títulos**: Máximo 60 caracteres
- **Descripciones breves**: 120-150 caracteres
- **Descripciones detalladas**: 200-500 palabras

### IDs
- **Formato**: Solo letras minúsculas, números y guiones
- **Ejemplos válidos**: `quiropodologia`, `diego-mateos`, `clinica-madrid-2024`
- **Ejemplos inválidos**: `Quiropodología`, `diego mateos`, `clínica_madrid`

## 🔍 Iconos Disponibles

Para servicios, usa estos iconos de Material Symbols:
- `medical_services` - Servicios médicos generales
- `healing` - Tratamientos curativos
- `analytics` - Análisis y estudios
- `sports` - Podología deportiva
- `child_care` - Servicios infantiles
- `elderly` - Servicios para mayores
- `science` - Investigación y tecnología

## ⚠️ Errores Comunes

### Error de Sintaxis JSON
**Problema**: Coma extra o faltante
```json
// ❌ Incorrecto
{
  "id": "servicio1",
  "title": "Servicio",  // ← Coma extra
}

// ✅ Correcto
{
  "id": "servicio1",
  "title": "Servicio"   // ← Sin coma al final
}
```

### ID Duplicado
**Problema**: Dos elementos con el mismo `id`
**Solución**: Cada `id` debe ser único en todo el archivo

### Imagen No Encontrada
**Problema**: URL de imagen incorrecta
**Solución**: Verificar que la imagen existe en la ruta especificada

## 🔧 Herramientas Útiles

### Validadores JSON Online
- [JSONLint](https://jsonlint.com/) - Valida sintaxis JSON
- [JSON Formatter](https://jsonformatter.org/) - Formatea y valida JSON

### Editores Recomendados
- **Visual Studio Code** - Con extensión JSON
- **Sublime Text** - Con resaltado de sintaxis
- **Notepad++** - Gratuito y simple

## 📞 Soporte

Si encuentras problemas:
1. **Verifica** la sintaxis JSON con un validador online
2. **Revisa** que todos los campos obligatorios estén presentes
3. **Comprueba** que las URLs de imágenes sean correctas
4. **Contacta** al equipo técnico si persisten los problemas

---

**Recuerda**: Siempre haz una copia de seguridad antes de realizar cambios importantes.
