---
export interface Props {
	image: string;
	name: string;
	position: string;
	description: string;
	alt: string;
	miembroId: string;
	// Props para navegación dual
	enableModal?: boolean;
	enablePageNavigation?: boolean;
}

const { 
	image, 
	name, 
	position, 
	description, 
	alt, 
	miembroId,
	enableModal = false,
	enablePageNavigation = true
} = Astro.props;
---

<div
	class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer flex flex-col"
	{...enableModal
		? { "data-modal-target": `modal-equipo-${miembroId}` }
		: {}}
>
	<!-- Imagen -->
	<div class="h-64 overflow-hidden">
		<img
			src={image}
			alt={alt}
			class="w-full h-full object-cover transition-transform hover:scale-110 duration-500"
		/>
	</div>

	<!-- Contenido -->
	<div class="p-6 flex flex-col flex-1 gap-4">
		<div class="text-center">
			<h3 class="font-bold text-xl mb-2 text-gray-800">{name}</h3>
			<p class="text-primary-600 font-medium mb-3">{position}</p>
			<p class="text-gray-600 text-sm">{description}</p>
		</div>

		<!-- Botones de acción -->
		<div class="mt-auto flex flex-col gap-3">
			{enableModal && (
				<button
					data-modal-target={`modal-equipo-${miembroId}`}
					class="text-primary-600 hover:text-primary-800 font-medium inline-flex items-center justify-center transition-colors"
				>
					Ver en modal
					<span class="material-symbols-outlined text-sm ml-1">
						open_in_new
					</span>
				</button>
			)}
			
			{enablePageNavigation && (
				<a
					href={`/equipo/${miembroId}`}
					class="text-primary-600 hover:text-primary-800 font-medium inline-flex items-center justify-center transition-colors"
				>
					Ver perfil completo
					<span class="material-symbols-outlined text-sm ml-1">
						arrow_forward
					</span>
				</a>
			)}
		</div>
	</div>
</div>
