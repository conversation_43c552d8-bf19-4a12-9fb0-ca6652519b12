---
import {
	<PERSON>dalBase,
	ModalContacto,
	ModalServicio,
} from "../components/ui/modals";
import Header from "../components/Header.astro";
import Footer from "../components/Footer.astro";
import MobileDrawer from "../components/MobileDrawer.astro";

// Scripts y estilos
import "../styles/global.css";

// Elementos de navegación
const navItems = [
	{ href: "#inicio", label: "Inicio" },
	{ href: "#servicios", label: "Servicios" },
	{ href: "#equipo", label: "Equipo" },
	{ href: "#ubicacion", label: "Ubicación y Contacto" },
	// { href: "#contacto", label: "Contacto" },
];
---

<html lang="es" class="scroll-smooth" style="scrollbar-gutter: stable;">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<!-- Material Symbols y Google Fonts -->
		<link
			rel="stylesheet"
			href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined"
		/>

		<link
			rel="stylesheet"
			href="https://fonts.googleapis.com/css2?family=Montserrat"
		/>

		<link
			rel="stylesheet"
			href="https://fonts.googleapis.com/css2?family=Open+Sans"
		/>

		<!-- Favicons -->
		<link
			rel="apple-touch-icon"
			sizes="180x180"
			href="/apple-touch-icon.png"
		/>
		<link
			rel="icon"
			type="image/png"
			sizes="32x32"
			href="/favicon-32x32.png"
		/>
		<link
			rel="icon"
			type="image/png"
			sizes="16x16"
			href="/favicon-16x16.png"
		/>
		<link rel="manifest" href="/site.webmanifest" />

		<!-- SEO -->
		<title>Clínica Podial</title>
	</head>
	<body class="font-sans bg-neutral-light min-h-screen pt-20">
		<Header navItems={navItems} />
		<main class="relative z-1">
			<slot />
		</main>
		<Footer />

		<!-- Modal de contacto -->
		<ModalContacto id="citaModal" />

		<!-- Drawer de navegación móvil -->
		<MobileDrawer navItems={navItems} />

		<!-- Cargar solo el modal manager optimizado -->
		<script src="../components/scripts/modal-manager.js"></script>
	</body>
</html>
