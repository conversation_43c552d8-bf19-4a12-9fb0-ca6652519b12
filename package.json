{"name": "@lbc/astro-starter-kit", "type": "module", "version": "0.0.1", "private": true, "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "update-styles": "git subtree pull --prefix src/styles https://github.com/LBC-Starter-Kits/sass-7-1-pattern.git main --squash", "deploy": "git push origin main:deployment_vps"}, "dependencies": {"@tailwindcss/vite": "^4.1.13", "astro": "^5.13.7", "sass": "^1.92.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.13", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.13"}}