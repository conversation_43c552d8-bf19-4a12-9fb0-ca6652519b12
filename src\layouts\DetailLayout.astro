---
import Layout from './Layout.astro';

export interface Props {
	title: string;
	description: string;
	image?: string;
	breadcrumbs?: Array<{
		name: string;
		href: string;
	}>;
}

const { title, description, image, breadcrumbs = [] } = Astro.props;
---

<Layout title={title} description={description}>
	<main class="min-h-screen bg-gray-50">
		<!-- Breadcrumbs -->
		{breadcrumbs.length > 0 && (
			<nav class="bg-white border-b border-gray-200">
				<div class="container mx-auto px-4 py-3">
					<ol class="flex items-center space-x-2 text-sm text-gray-600">
						<li>
							<a href="/" class="hover:text-primary-600 transition-colors">
								<span class="material-symbols-outlined text-base">home</span>
							</a>
						</li>
						{breadcrumbs.map((crumb, index) => (
							<li class="flex items-center">
								<span class="material-symbols-outlined text-gray-400 mx-2">chevron_right</span>
								{index === breadcrumbs.length - 1 ? (
									<span class="text-gray-900 font-medium">{crumb.name}</span>
								) : (
									<a href={crumb.href} class="hover:text-primary-600 transition-colors">
										{crumb.name}
									</a>
								)}
							</li>
						))}
					</ol>
				</div>
			</nav>
		)}

		<!-- Hero Section -->
		<section class="bg-white">
			<div class="container mx-auto px-4 py-12">
				<div class="max-w-4xl mx-auto">
					{image && (
						<div class="mb-8 rounded-xl overflow-hidden shadow-lg">
							<img 
								src={image} 
								alt={title}
								class="w-full h-64 md:h-80 object-cover"
							/>
						</div>
					)}
					
					<div class="text-center mb-8">
						<h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
							{title}
						</h1>
						<p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
							{description}
						</p>
					</div>
				</div>
			</div>
		</section>

		<!-- Content Section -->
		<section class="py-12">
			<div class="container mx-auto px-4">
				<div class="max-w-4xl mx-auto">
					<slot />
				</div>
			</div>
		</section>

		<!-- Back Button -->
		<section class="py-8 bg-white border-t border-gray-200">
			<div class="container mx-auto px-4">
				<div class="max-w-4xl mx-auto text-center">
					<button 
						onclick="history.back()" 
						class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
					>
						<span class="material-symbols-outlined mr-2">arrow_back</span>
						Volver
					</button>
				</div>
			</div>
		</section>
	</main>
</Layout>
