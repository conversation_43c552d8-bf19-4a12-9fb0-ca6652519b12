---
export interface Props {
	image: string;
	title: string;
	description: string;
	alt: string;
	servicioId: string;
	// Nuevas props para navegación dual
	enableModal?: boolean;
	enablePageNavigation?: boolean;
}

const {
	image,
	title,
	description,
	alt,
	servicioId,
	enableModal = true,
	enablePageNavigation = true,
} = Astro.props;
---

<div
	class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer flex flex-col"
	{...enableModal
		? { "data-modal-target": `modal-servicio-${servicioId}` }
		: {}}
>
	<!-- Imagen -->
	<div class="h-48 overflow-hidden">
		<img
			src={image}
			alt={alt}
			class="w-full h-full object-cover transition-transform hover:scale-110 duration-500"
		/>
	</div>

	<!-- Contenido -->
	<div class="p-6 flex flex-col flex-1 gap-6">
		<div>
			<h3 class="font-bold text-xl mb-2 text-gray-800">{title}</h3>
			<p class="text-gray-600">{description}</p>
		</div>

		<!-- Bo<PERSON> de acción -->
		<div class="mt-auto flex flex-col sm:flex-row gap-3">
			{
				enableModal && (
					<button
						data-modal-target={`modal-servicio-${servicioId}`}
						class="text-primary-600 hover:text-primary-800 font-medium inline-flex items-center justify-center transition-colors"
					>
						Ver más detalles
						<span class="material-symbols-outlined text-sm ml-1">
							open_in_new
						</span>
					</button>
				)
			}

			{
				enablePageNavigation && (
					<a
						href={`/servicios/${servicioId}`}
						class="text-primary-600 hover:text-primary-800 font-medium inline-flex items-center justify-center transition-colors"
					>
						Ver más detalles
						<span class="material-symbols-outlined text-sm ml-1">
							arrow_forward
						</span>
					</a>
				)
			}
		</div>
	</div>
</div>
