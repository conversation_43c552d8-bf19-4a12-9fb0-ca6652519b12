---
/**
 * ItemListExample - Ejemplos de uso del componente ItemList genérico
 */
import ItemList from "./ItemList.astro";

// Datos de ejemplo
const beneficios = [
	"Mejora la salud y bienestar general",
	"Reduce el dolor y las molestias",
	"Previene problemas futuros",
	"Tratamiento personalizado y profesional"
];

const procesos = [
	"Evaluación inicial y diagnóstico",
	"Diseño del plan de tratamiento",
	"Aplicación del tratamiento",
	"Seguimiento y control"
];

const caracteristicas = [
	"Tecnología de última generación",
	"Profesionales certificados",
	"Ambiente cómodo y relajante",
	"Horarios flexibles"
];

const ventajas = [
	"Sin dolor durante el tratamiento",
	"Resultados visibles inmediatos",
	"Tratamiento personalizado",
	"Garantía de satisfacción"
];
---

<div class="p-8 space-y-12">
	<h2 class="text-3xl font-bold mb-6">Ejemplos de ItemList - Componente Genérico</h2>
	
	<!-- Lista de beneficios (por defecto) -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">1. Lista de Beneficios (Configuración por Defecto)</h3>
		<ItemList 
			items={beneficios}
			titulo="Beneficios"
		/>
	</div>

	<!-- Lista de procesos (ordenada) -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">2. Lista de Procesos (Ordenada)</h3>
		<ItemList 
			items={procesos}
			titulo="¿Cómo funciona?"
			titleIcon="list"
			titleIconColor="text-blue-600"
			listType="ordered"
		/>
	</div>

	<!-- Lista con imagen -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">3. Lista con Imagen</h3>
		<ItemList 
			items={caracteristicas}
			titulo="Características"
			titleIcon="star"
			titleIconColor="text-purple-600"
			icon="star"
			iconColor="text-purple-500"
			imagen="/hero_podial_1.png"
			imagenAlt="Características del servicio"
		/>
	</div>

	<!-- Lista personalizada -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">4. Lista Personalizada</h3>
		<ItemList 
			items={ventajas}
			titulo="Ventajas Exclusivas"
			titleIcon="verified"
			titleIconColor="text-green-600"
			icon="thumb_up"
			iconColor="text-green-500"
			class="bg-green-50 p-6 rounded-xl"
			titleClass="text-green-800"
			itemClass="bg-white p-2 rounded-lg shadow-sm"
		/>
	</div>

	<!-- Lista sin título -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">5. Lista sin Título</h3>
		<ItemList 
			items={beneficios}
			showTitle={false}
			icon="check_circle"
			iconColor="text-blue-500"
		/>
	</div>

	<!-- Lista con layout forzado -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">6. Layout de Una Columna (con imagen disponible)</h3>
		<ItemList 
			items={procesos}
			titulo="Proceso Simplificado"
			listType="ordered"
			imagen="/hero_podial_1.png"
			layout="single"
		/>
	</div>

	<!-- Comparación lado a lado -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">7. Comparación: Beneficios vs Procesos</h3>
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
			<ItemList 
				items={beneficios}
				titulo="Beneficios"
				titleIcon="check_circle"
				titleIconColor="text-green-600"
				icon="check"
				iconColor="text-green-500"
				class="bg-green-50 p-4 rounded-lg"
			/>
			<ItemList 
				items={procesos}
				titulo="Proceso"
				titleIcon="list"
				titleIconColor="text-blue-600"
				listType="ordered"
				class="bg-blue-50 p-4 rounded-lg"
			/>
		</div>
	</div>

	<!-- Tema oscuro -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">8. Tema Oscuro</h3>
		<ItemList 
			items={caracteristicas}
			titulo="Características Premium"
			titleIcon="diamond"
			titleIconColor="text-yellow-400"
			icon="star"
			iconColor="text-yellow-400"
			imagen="/hero_podial_1.png"
			class="bg-gray-900 p-6 rounded-xl"
			titleClass="!text-white"
			listClass="space-y-3"
			itemClass="bg-gray-800 p-3 rounded-lg"
		/>
	</div>

	<!-- Documentación -->
	<div class="bg-blue-50 p-6 rounded-lg">
		<h3 class="text-xl font-semibold mb-4">📚 Documentación de ItemList</h3>
		
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			<div>
				<h4 class="font-semibold mb-2">Props Principales:</h4>
				<ul class="space-y-1 text-sm">
					<li><code class="bg-gray-200 px-2 py-1 rounded">items</code> - Array de strings (requerido)</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">titulo</code> - Título de la sección</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">listType</code> - 'unordered' | 'ordered'</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">icon</code> - Icono para items</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">titleIcon</code> - Icono del título</li>
				</ul>
			</div>
			
			<div>
				<h4 class="font-semibold mb-2">Valores por Defecto:</h4>
				<ul class="space-y-1 text-sm">
					<li>• <strong>titulo:</strong> "Lista"</li>
					<li>• <strong>listType:</strong> "unordered"</li>
					<li>• <strong>icon:</strong> "check"</li>
					<li>• <strong>iconColor:</strong> "text-green-500"</li>
					<li>• <strong>titleIcon:</strong> "check_circle"</li>
					<li>• <strong>titleIconColor:</strong> "text-green-600"</li>
				</ul>
			</div>
		</div>

		<div class="mt-4 p-4 bg-white rounded border-l-4 border-blue-500">
			<h4 class="font-semibold mb-2">🎯 Casos de Uso Comunes:</h4>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
				<div>
					<h5 class="font-semibold">Beneficios:</h5>
					<pre class="bg-gray-100 p-2 rounded text-xs"><code>&lt;ItemList 
  items={beneficios}
  titulo="Beneficios"
  titleIcon="check_circle"
  titleIconColor="text-green-600"
  icon="check"
  iconColor="text-green-500"
/&gt;</code></pre>
				</div>
				<div>
					<h5 class="font-semibold">Procesos:</h5>
					<pre class="bg-gray-100 p-2 rounded text-xs"><code>&lt;ItemList 
  items={procesos}
  titulo="¿Cómo funciona?"
  titleIcon="list"
  titleIconColor="text-blue-600"
  listType="ordered"
/&gt;</code></pre>
				</div>
			</div>
		</div>
	</div>
</div>
