---
/**
 * Button - Componente reutilizable para botones de llamada a la acción
 *
 * Este componente muestra un botón CTA con mensaje personalizable.
 * Diseñado para ser completamente reutilizable en otros proyectos.
 *
 * @param textoBoton - Texto del botón
 * @param modalTarget - ID del modal objetivo (opcional)
 * @param closeModal - Si debe cerrar el modal actual (default: false)
 * @param onClick - Función onClick personalizada (opcional)
 * @param class - Clases adicionales (opcional)
 */

export interface Props {
	textoBoton: string;
	modalTarget?: string;
	closeModal?: boolean;
	onClick?: string;
	class?: string;
}

const {
	textoBoton,
	modalTarget,
	closeModal = false,
	onClick,
	class: className = "",
} = Astro.props;
---

<button
	class={`bg-primary-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary-700 transition-colors shadow-lg hover:shadow-xl ${className}`}
	data-modal-target={modalTarget}
	data-modal-close={closeModal ? "" : undefined}
	onclick={onClick}
>
	{textoBoton}
</button>
