---
/**
 * ImageGallery - Componente reutilizable para mostrar galería de imágenes
 *
 * Este componente muestra una galería de imágenes en formato grid responsive.
 * Incluye funcionalidad de lightbox para ver imágenes a pantalla completa.
 * Diseñado para ser completamente reutilizable en otros proyectos.
 *
 * @param imagenes - Array de URLs de imágenes
 * @param titulo - Título del servicio/elemento para alt text
 * @param showTitle - Si mostrar o no el título de la galería (default: true)
 * @param enableLightbox - Si habilitar el lightbox de pantalla completa (default: true)
 */

export interface Props {
	imagenes: string[];
	titulo: string;
	showTitle?: boolean;
	enableLightbox?: boolean;
}

const {
	imagenes,
	titulo,
	showTitle = true,
	enableLightbox = true,
} = Astro.props;

// Generar un ID único para esta galería
const galleryId = `gallery-${Math.random().toString(36).substring(2, 11)}`;
---

{
	imagenes && imagenes.length > 0 && (
		<div class="">
			{showTitle && (
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-purple-600 mr-2">
						photo_library
					</span>
					Galería
				</h3>
			)}
			<div class="grid grid-cols-2 md:grid-cols-3 gap-3">
				{imagenes.map((imagen, index) => (
					<img
						src={imagen}
						alt={`${titulo} ${index + 1}`}
						class="rounded-lg object-cover h-24 w-full hover:scale-105 transition-transform cursor-pointer"
						data-lightbox-gallery={enableLightbox ? galleryId : undefined}
						data-lightbox-index={enableLightbox ? index : undefined}
						data-lightbox-src={enableLightbox ? imagen : undefined}
						data-lightbox-alt={
							enableLightbox ? `${titulo} ${index + 1}` : undefined
						}
					/>
				))}
			</div>

			{/* Lightbox Modal */}
			{enableLightbox && (
				<div
					id={`lightbox-${galleryId}`}
					class="lightbox-overlay fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center"
				>
					{/* Botón cerrar mejorado */}
					<button
						class="lightbox-close absolute top-4 right-4 bg-red bg-opacity-50 hover:bg-opacity-70 text-white rounded-full w-12 h-12 flex items-center justify-center transition-all duration-200 z-20 hover:scale-110"
						aria-label="Cerrar galería"
					>
						<span class="material-symbols-outlined text-2xl">close</span>
					</button>

					{/* Navegación anterior */}
					<button
						class="lightbox-prev absolute left-2 md:left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full w-10 h-10 md:w-12 md:h-12 flex items-center justify-center transition-all duration-200 z-10 hover:scale-110"
						aria-label="Imagen anterior"
					>
						<span class="material-symbols-outlined text-xl md:text-2xl">
							chevron_left
						</span>
					</button>

					{/* Navegación siguiente */}
					<button
						class="lightbox-next absolute right-2 md:right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full w-10 h-10 md:w-12 md:h-12 flex items-center justify-center transition-all duration-200 z-10 hover:scale-110"
						aria-label="Imagen siguiente"
					>
						<span class="material-symbols-outlined text-xl md:text-2xl">
							chevron_right
						</span>
					</button>

					{/* Contenedor de imagen responsive */}
					<div class="lightbox-content w-full h-full p-4 md:p-8 flex items-center justify-center">
						<img
							class="lightbox-image w-[90%] h-[90%] sm:w-[85%] sm:h-[85%] md:w-[80%] md:h-[80%] lg:w-[75%] lg:h-[75%] xl:w-[70%] xl:h-[70%] object-contain"
							src=""
							alt=""
						/>
					</div>

					{/* Contador de imágenes */}
					<div class="lightbox-counter absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm md:text-base">
						<span class="current-image">1</span> /{" "}
						<span class="total-images">{imagenes.length}</span>
					</div>
				</div>
			)}
		</div>
	)
}

<script>
	// Lightbox functionality
	document.addEventListener("DOMContentLoaded", () => {
		// Buscar todas las galerías con lightbox
		const galleries = document.querySelectorAll("[data-lightbox-gallery]");

		galleries.forEach((img) => {
			const galleryId = img.getAttribute("data-lightbox-gallery");
			const lightbox = document.getElementById(`lightbox-${galleryId}`);

			if (!lightbox) return;

			const lightboxImage = lightbox.querySelector(".lightbox-image");
			const currentImageSpan = lightbox.querySelector(".current-image");
			const closeBtn = lightbox.querySelector(".lightbox-close");
			const prevBtn = lightbox.querySelector(".lightbox-prev");
			const nextBtn = lightbox.querySelector(".lightbox-next");

			// Obtener todas las imágenes de esta galería
			const galleryImages = Array.from(
				document.querySelectorAll(`[data-lightbox-gallery="${galleryId}"]`)
			);

			let currentIndex = 0;

			// Función para mostrar imagen
			function showImage(index: number) {
				const targetImg = galleryImages[index];
				if (!targetImg || !lightboxImage || !currentImageSpan) return;

				const src = targetImg.getAttribute("data-lightbox-src");
				const alt = targetImg.getAttribute("data-lightbox-alt");

				if (src) lightboxImage.src = src;
				if (alt) lightboxImage.alt = alt;
				currentImageSpan.textContent = (index + 1).toString();
				currentIndex = index;

				// Mostrar/ocultar botones de navegación
				if (prevBtn && prevBtn instanceof HTMLElement) {
					prevBtn.style.display =
						galleryImages.length > 1 ? "flex" : "none";
				}
				if (nextBtn && nextBtn instanceof HTMLElement) {
					nextBtn.style.display =
						galleryImages.length > 1 ? "flex" : "none";
				}
			}

			// Función para abrir lightbox
			function openLightbox(index: number) {
				if (!lightbox) return;
				showImage(index);
				lightbox.classList.remove("hidden");
				document.body.style.overflow = "hidden";
			}

			// Función para cerrar lightbox
			function closeLightbox() {
				if (!lightbox) return;
				lightbox.classList.add("hidden");
				document.body.style.overflow = "";
			}

			// Función para navegar
			function navigate(direction: number) {
				let newIndex = currentIndex + direction;

				// Navegación circular
				if (newIndex < 0) {
					newIndex = galleryImages.length - 1;
				} else if (newIndex >= galleryImages.length) {
					newIndex = 0;
				}

				showImage(newIndex);
			}

			// Event listeners para esta galería
			img.addEventListener("click", () => {
				const indexStr = img.getAttribute("data-lightbox-index");
				if (indexStr) {
					const index = parseInt(indexStr);
					openLightbox(index);
				}
			});

			// Event listeners del lightbox (solo una vez por galería)
			if (!lightbox.hasAttribute("data-listeners-added")) {
				lightbox.setAttribute("data-listeners-added", "true");

				// Cerrar lightbox
				if (closeBtn) {
					closeBtn.addEventListener("click", closeLightbox);
				}

				// Navegación
				if (prevBtn) {
					prevBtn.addEventListener("click", () => navigate(-1));
				}
				if (nextBtn) {
					nextBtn.addEventListener("click", () => navigate(1));
				}

				// Cerrar con click en overlay
				lightbox.addEventListener("click", (e) => {
					if (e.target === lightbox) {
						closeLightbox();
					}
				});

				// Navegación con teclado
				document.addEventListener("keydown", (e) => {
					if (!lightbox.classList.contains("hidden")) {
						switch (e.key) {
							case "Escape":
								closeLightbox();
								break;
							case "ArrowLeft":
								navigate(-1);
								break;
							case "ArrowRight":
								navigate(1);
								break;
						}
					}
				});
			}
		});
	});
</script>
