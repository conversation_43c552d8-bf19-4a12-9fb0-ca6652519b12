---
/**
 * ImageGallery - Componente reutilizable para mostrar galería de imágenes
 *
 * Este componente muestra una galería de imágenes en formato grid responsive.
 * Diseñado para ser completamente reutilizable en otros proyectos.
 *
 * @param imagenes - Array de URLs de imágenes
 * @param titulo - Título del servicio/elemento para alt text
 * @param showTitle - Si mostrar o no el título de la galería (default: true)
 */

export interface Props {
	imagenes: string[];
	titulo: string;
	showTitle?: boolean;
}

const { imagenes, titulo, showTitle = true } = Astro.props;
---

{
	imagenes && imagenes.length > 0 && (
		<div class="">
			{showTitle && (
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-purple-600 mr-2">
						photo_library
					</span>
					Galería
				</h3>
			)}
			<div class="grid grid-cols-2 md:grid-cols-3 gap-3">
				{imagenes.map((imagen, index) => (
					<img
						src={imagen}
						alt={`${titulo} ${index + 1}`}
						class="rounded-lg object-cover h-24 w-full hover:scale-105 transition-transform cursor-pointer"
					/>
				))}
			</div>
		</div>
	)
}
