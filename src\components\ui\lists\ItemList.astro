---
/**
 * ItemList - Componente reutilizable para mostrar listas genéricas
 *
 * Este componente muestra listas con iconos personalizables.
 * Puede usarse para beneficios, procesos, características, etc.
 * Diseñado para ser completamente reutilizable en otros proyectos.
 *
 * @param items - Array de strings con los elementos de la lista
 * @param titulo - Título de la sección
 * @param showTitle - Si mostrar o no el título (default: true)
 * @param imagen - URL de imagen opcional para mostrar al lado
 * @param imagenAlt - Alt text para la imagen
 * @param showImage - Si mostrar la imagen cuando está disponible (default: true)
 * @param layout - Layout: 'single' | 'with-image' (default: 'auto')
 * @param listType - Tipo de lista: 'unordered' | 'ordered' (default: 'unordered')
 * @param icon - Icono para items no ordenados (default: 'check')
 * @param iconColor - Color del icono (default: 'text-green-500')
 * @param titleIcon - Icono del título (default: 'check_circle')
 * @param titleIconColor - Color del icono del título (default: 'text-green-600')
 * @param class - Clases adicionales para el contenedor
 * @param titleClass - Clases adicionales para el título
 * @param listClass - Clases adicionales para la lista
 * @param itemClass - Clases adicionales para cada item
 * @param iconClass - Clases adicionales para los iconos
 */

export interface Props {
	items: string[];
	titulo?: string;
	showTitle?: boolean;
	imagen?: string;
	imagenAlt?: string;
	showImage?: boolean;
	layout?: 'single' | 'with-image' | 'auto';
	listType?: 'unordered' | 'ordered';
	icon?: string;
	iconColor?: string;
	titleIcon?: string;
	titleIconColor?: string;
	class?: string;
	titleClass?: string;
	listClass?: string;
	itemClass?: string;
	iconClass?: string;
}

const {
	items,
	titulo = "Lista",
	showTitle = true,
	imagen,
	imagenAlt = "",
	showImage = true,
	layout = 'auto',
	listType = 'unordered',
	icon = 'check',
	iconColor = 'text-green-500',
	titleIcon = 'check_circle',
	titleIconColor = 'text-green-600',
	class: className = "",
	titleClass = "",
	listClass = "",
	itemClass = "",
	iconClass = "",
} = Astro.props;

// Determinar el layout automáticamente
const shouldShowImage = showImage && imagen && layout !== 'single';
const finalLayout = layout === 'auto' ? (shouldShowImage ? 'with-image' : 'single') : layout;
const useGridLayout = finalLayout === 'with-image' && shouldShowImage;
---

{
	items && items.length > 0 && (
		<div class={`${useGridLayout ? 'grid grid-cols-1 md:grid-cols-2 gap-6 items-start' : ''} ${className}`}>
			<!-- Lista de items -->
			<div>
				{showTitle && (
					<h3 class={`font-semibold text-gray-800 mb-3 flex items-center ${titleClass}`}>
						<span class={`material-symbols-outlined ${titleIconColor} mr-2 ${iconClass}`}>
							{titleIcon}
						</span>
						{titulo}
					</h3>
				)}
				
				{listType === 'unordered' ? (
					<ul class={`space-y-2 ${listClass}`}>
						{items.map((item) => (
							<li class={`flex items-start ${itemClass}`}>
								<span class={`material-symbols-outlined ${iconColor} text-xl mr-2 mt-0.5 ${iconClass}`}>
									{icon}
								</span>
								<span class="text-gray-700">{item}</span>
							</li>
						))}
					</ul>
				) : (
					<ol class={`space-y-3 ${listClass}`}>
						{items.map((item, index) => (
							<li class={`flex items-start ${itemClass}`}>
								<div class="bg-primary-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold mr-3 mt-0.5 flex-shrink-0">
									{index + 1}
								</div>
								<span class="text-gray-700">{item}</span>
							</li>
						))}
					</ol>
				)}
			</div>

			<!-- Imagen si existe y está habilitada -->
			{shouldShowImage && (
				<div class="overflow-hidden rounded-xl shadow-lg">
					<img
						src={imagen}
						alt={imagenAlt || `${titulo} imagen`}
						class="w-full h-full object-cover transition-transform hover:scale-105 duration-500"
					/>
				</div>
			)}
		</div>
	)
}
