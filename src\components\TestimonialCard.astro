---
const { text, name, subtitle, img, stars = 5 } = Astro.props;
---

<div
	class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow"
>
	<div class="flex items-center mb-4 text-primary-500">
		{
			Array.from({ length: stars }).map(() => (
				<span class="material-symbols-outlined">star</span>
			))
		}
	</div>
	<p class="text-gray-600 italic mb-6">"{text}"</p>
	<div class="flex items-center">
		<div
			class="hidden w-12 h-12 rounded-full bg-gray-200 overflow-hidden mr-4"
		>
			<img src={img} alt={name} class="w-full h-full object-cover" />
		</div>
		<div>
			<h4 class="font-bold text-gray-800">{name}</h4>
			<p class="text-gray-500 text-sm">{subtitle}</p>
		</div>
	</div>
</div>
