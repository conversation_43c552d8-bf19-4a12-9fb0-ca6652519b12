# Guía de Migración - De Modales a Páginas Dinámicas

## 🎯 Introducción

Esta guía te ayuda a migrar gradualmente desde un sistema basado únicamente en modales hacia el nuevo sistema de páginas dinámicas, manteniendo la compatibilidad durante la transición.

## 🔄 Estrategias de Migración

### 1. Migración Gradual (Recomendada)

Mantén ambos sistemas funcionando mientras migras progresivamente.

**Ventajas**:
- ✅ Sin interrupciones en el servicio
- ✅ Posibilidad de rollback inmediato
- ✅ Testing gradual de funcionalidades
- ✅ Feedback de usuarios durante la transición

**Fases**:
1. **Fase 1**: Implementar páginas dinámicas manteniendo modales
2. **Fase 2**: Dirigir tráfico gradualmente a páginas
3. **Fase 3**: Desactivar modales progresivamente
4. **Fase 4**: Limpieza final del código de modales

### 2. Migración Completa

Cambio directo de modales a páginas.

**Ventajas**:
- ✅ Implementación más rápida
- ✅ Código más limpio desde el inicio
- ✅ Mejor SEO inmediato

**Desventajas**:
- ❌ Mayor riesgo de problemas
- ❌ Cambio abrupto para usuarios
- ❌ Más difícil hacer rollback

## 📋 Plan de Migración Paso a Paso

### Fase 1: Preparación (Semana 1)

#### 1.1 Auditoría del Sistema Actual
```bash
# Identificar todos los modales existentes
grep -r "data-modal-target" src/components/
grep -r "ModalServicio" src/components/
```

#### 1.2 Backup de Datos
```bash
# Crear backup de archivos críticos
cp src/data/servicios.json src/data/servicios.json.backup
cp src/components/Servicios.astro src/components/Servicios.astro.backup
```

#### 1.3 Documentar URLs Actuales
```
Páginas existentes:
- / (página principal con modales)
- /#servicios (sección con modales)
- /#equipo (sección con información estática)

URLs que se crearán:
- /servicios (índice de servicios)
- /servicios/[slug] (páginas individuales)
- /equipo (índice del equipo)
- /equipo/[slug] (perfiles individuales)
```

### Fase 2: Implementación Base (Semana 2)

#### 2.1 Crear Estructura de Datos
```typescript
// src/data/equipo.json - Migrar datos hardcodeados
{
  "equipo": [
    {
      "id": "diego-mateos",
      "image": "https://clinicapodial.com/hubfs/IMG_5813.jpeg",
      "name": "Diego Mateos",
      "position": "Podólogo Especialista",
      "description": "Con más de 20 años de experiencia...",
      "alt": "Diego Mateos - Podólogo Especialista",
      "miembro": {
        // ... datos detallados
      }
    }
  ]
}
```

#### 2.2 Implementar Páginas Dinámicas
```astro
<!-- src/pages/servicios/[slug].astro -->
<!-- Implementar según documentación -->
```

#### 2.3 Configurar Navegación Dual
```astro
<!-- Mantener ambas opciones disponibles -->
<ServiceCard
  enableModal={true}          // ← Mantener modales
  enablePageNavigation={true} // ← Agregar páginas
  {...props}
/>
```

### Fase 3: Testing y Optimización (Semana 3)

#### 3.1 Testing de URLs
```bash
# Verificar que todas las páginas se generan correctamente
npm run build
ls -la dist/servicios/
ls -la dist/equipo/
```

#### 3.2 Testing de SEO
```html
<!-- Verificar meta tags en páginas generadas -->
<head>
  <title>Quiropodología | Clínica Podológica</title>
  <meta name="description" content="...">
</head>
```

#### 3.3 Testing de Performance
```bash
# Medir tiempo de build
time npm run build

# Verificar tamaño de páginas generadas
du -sh dist/servicios/*
```

### Fase 4: Migración de Tráfico (Semana 4)

#### 4.1 Configurar Redirecciones Internas
```astro
<!-- En componentes principales, priorizar páginas -->
<ServiceCard
  enableModal={false}         // ← Desactivar modales gradualmente
  enablePageNavigation={true} // ← Priorizar páginas
/>
```

#### 4.2 Actualizar Enlaces Internos
```astro
<!-- Cambiar enlaces de modales a páginas -->
<!-- Antes -->
<a href="#" data-modal-target="modal-servicio-quiropodologia">
  Ver servicio
</a>

<!-- Después -->
<a href="/servicios/quiropodologia">
  Ver servicio
</a>
```

#### 4.3 Implementar Analytics
```javascript
// Tracking de uso de páginas vs modales
gtag('event', 'page_view', {
  page_title: 'Servicio Individual',
  page_location: '/servicios/quiropodologia'
});
```

### Fase 5: Limpieza Final (Semana 5)

#### 5.1 Remover Código de Modales
```astro
<!-- Eliminar componentes de modales no utilizados -->
<!-- src/components/ui/modals/ModalServicio.astro -->
```

#### 5.2 Actualizar Configuración
```astro
<!-- Configuración final de componentes -->
<ServiceCard
  enableModal={false}         // ← Solo páginas
  enablePageNavigation={true}
/>
```

#### 5.3 Optimizar Build
```bash
# Verificar que el build es más eficiente
npm run build
# Verificar que no hay referencias a modales
grep -r "modal-servicio" dist/
```

## 🔧 Configuraciones de Transición

### Configuración A/B Testing

```astro
---
// Permitir testing de ambas versiones
const usePages = Astro.url.searchParams.get('pages') === 'true';
---

<ServiceCard
  enableModal={!usePages}
  enablePageNavigation={usePages}
  {...props}
/>
```

### Configuración por Sección

```astro
---
// Migrar secciones gradualmente
const serviciosUsePages = true;  // ← Ya migrado
const equipoUsePages = false;    // ← Aún en modales
const proyectosUsePages = true;  // ← Ya migrado
---
```

### Configuración por Dispositivo

```astro
---
// Páginas en desktop, modales en mobile
const isMobile = Astro.request.headers.get('user-agent')?.includes('Mobile');
---

<ServiceCard
  enableModal={isMobile}
  enablePageNavigation={!isMobile}
  {...props}
/>
```

## 📊 Métricas de Migración

### KPIs a Monitorear

1. **Performance**
   - Tiempo de carga de páginas
   - Tiempo de build
   - Tamaño de bundle

2. **SEO**
   - Páginas indexadas
   - Posicionamiento de keywords
   - CTR desde buscadores

3. **UX**
   - Tiempo en página
   - Bounce rate
   - Conversiones

4. **Técnicas**
   - Errores 404
   - Errores de JavaScript
   - Tiempo de respuesta

### Herramientas de Monitoreo

```javascript
// Google Analytics 4
gtag('config', 'GA_MEASUREMENT_ID', {
  custom_map: {
    'custom_parameter_1': 'migration_phase'
  }
});

// Tracking de eventos específicos
gtag('event', 'modal_to_page_migration', {
  'event_category': 'Migration',
  'event_label': 'Service Page View',
  'value': 1
});
```

## 🚨 Plan de Rollback

### Rollback Rápido (< 5 minutos)

```bash
# 1. Restaurar archivos de backup
cp src/components/Servicios.astro.backup src/components/Servicios.astro

# 2. Revertir configuración de componentes
# Cambiar enableModal={false} a enableModal={true}

# 3. Rebuild y deploy
npm run build
npm run deploy
```

### Rollback Completo (< 30 minutos)

```bash
# 1. Revertir a commit anterior
git revert HEAD~5  # Revertir últimos 5 commits

# 2. Restaurar datos
cp src/data/*.backup src/data/

# 3. Limpiar páginas dinámicas
rm -rf src/pages/servicios/[slug].astro
rm -rf src/pages/equipo/[slug].astro

# 4. Rebuild completo
npm run clean
npm run build
```

## ✅ Checklist de Migración

### Pre-Migración
- [ ] Backup completo de archivos críticos
- [ ] Documentación de URLs actuales
- [ ] Plan de testing definido
- [ ] Métricas baseline establecidas

### Durante la Migración
- [ ] Páginas dinámicas funcionando
- [ ] SEO tags implementados
- [ ] Navegación dual configurada
- [ ] Testing en múltiples dispositivos

### Post-Migración
- [ ] Analytics configurado
- [ ] Performance optimizada
- [ ] Código de modales removido
- [ ] Documentación actualizada

## 🎯 Mejores Prácticas

### 1. Comunicación
- Informar al equipo sobre cambios
- Documentar decisiones técnicas
- Mantener changelog actualizado

### 2. Testing
- Probar en múltiples navegadores
- Verificar responsive design
- Validar accesibilidad

### 3. Performance
- Optimizar imágenes
- Minimizar JavaScript
- Implementar lazy loading

### 4. SEO
- Configurar meta tags únicos
- Implementar structured data
- Optimizar URLs

---

**Nota**: Esta migración debe realizarse en un entorno de desarrollo primero, con testing exhaustivo antes de aplicar en producción.
