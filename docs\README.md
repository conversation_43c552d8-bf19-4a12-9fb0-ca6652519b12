# Documentación del Sistema de Páginas Dinámicas

Este directorio contiene la documentación completa del sistema de páginas dinámicas implementado en la aplicación Astro.

## 📚 Índice de Documentación

### Para Desarrolladores
- [**Arquitectura del Sistema**](./arquitectura.md) - Estrategia técnica y decisiones de diseño
- [**Guía de Implementación**](./implementacion.md) - Cómo implementar nuevas páginas dinámicas
- [**API Reference**](./api-reference.md) - Documentación de funciones y tipos TypeScript

### Para Usuarios
- [**Guía de Uso**](./guia-uso.md) - Cómo usar el sistema de páginas dinámicas
- [**Gestión de Contenido**](./gestion-contenido.md) - Cómo agregar y modificar contenido

### Ejemplos y Casos de Uso
- [**Ejemplos Prácticos**](./ejemplos.md) - Casos de uso comunes con código
- [**Migración**](./migracion.md) - Cómo migrar de modales a páginas dinámicas

## 🚀 Inicio Rápido

El sistema de páginas dinámicas permite crear automáticamente páginas individuales para servicios, equipo y proyectos basándose en datos JSON.

### URLs Generadas Automáticamente:
- `/servicios/[id]` - Páginas de servicios individuales
- `/equipo/[id]` - Páginas de perfiles del equipo
- `/proyectos/[id]` - Páginas de proyectos individuales

### Características Principales:
- ✅ **Navegación Dual**: Modales + Páginas completas
- ✅ **SEO Optimizado**: Meta tags y URLs amigables
- ✅ **Responsive Design**: Adaptable a todos los dispositivos
- ✅ **Breadcrumbs**: Navegación contextual
- ✅ **Reutilizable**: Sistema extensible para nuevos tipos de contenido

## 🔧 Tecnologías Utilizadas

- **Astro**: Framework principal con generación estática
- **TypeScript**: Tipado fuerte para datos y componentes
- **Tailwind CSS**: Estilos utilitarios
- **JSON**: Almacenamiento de datos estructurados

## 📁 Estructura de Archivos

```
src/
├── data/                    # Datos JSON y utilidades TypeScript
│   ├── servicios.json      # Datos de servicios
│   ├── servicios.ts        # Utilidades para servicios
│   ├── equipo.json         # Datos del equipo
│   ├── equipo.ts           # Utilidades para equipo
│   ├── proyectos.json      # Datos de proyectos
│   └── proyectos.ts        # Utilidades para proyectos
├── layouts/
│   └── DetailLayout.astro  # Layout reutilizable para páginas de detalle
├── components/
│   ├── ServiceCard.astro   # Tarjeta de servicio con navegación dual
│   ├── EquipoCard.astro    # Tarjeta de miembro del equipo
│   └── ProyectoCard.astro  # Tarjeta de proyecto
└── pages/
    ├── servicios/
    │   ├── index.astro     # Lista de servicios
    │   └── [slug].astro    # Página dinámica de servicio
    ├── equipo/
    │   ├── index.astro     # Lista del equipo
    │   └── [slug].astro    # Página dinámica de miembro
    └── proyectos/
        ├── index.astro     # Lista de proyectos
        └── [slug].astro    # Página dinámica de proyecto
```

## 🎯 Próximos Pasos

1. Lee la [Guía de Uso](./guia-uso.md) para empezar a usar el sistema
2. Consulta la [Arquitectura](./arquitectura.md) para entender el diseño técnico
3. Revisa los [Ejemplos](./ejemplos.md) para casos de uso específicos

---

**Versión**: 1.0.0  
**Última actualización**: Enero 2025  
**Mantenedor**: Equipo de Desarrollo
