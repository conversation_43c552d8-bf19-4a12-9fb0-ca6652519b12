# Sistema de Modales Extensible

Este documento describe cómo usar y extender el sistema de modales del proyecto.

## Visión General

El sistema de modales está diseñado para ser completamente extensible, permitiendo agregar nuevos tipos de modales de forma sencilla y manteniendo la consistencia en toda la aplicación.

### Características principales:

-  **IDs consistentes**: Todos los modales siguen el patrón `modal-{tipo}-{id}`
-  **Gestión automática**: El ModalManager detecta automáticamente el tipo de modal
-  **Extensible**: Fácil agregar nuevos tipos de modales
-  **TypeScript**: Completamente tipado para mejor desarrollo
-  **Reutilizable**: Componentes base que se pueden extender

## Estructura del Sistema

```
src/
├── components/
│   ├── scripts/
│   │   └── modal-manager.js          # Gestor principal de modales
│   └── ui/
│       └── modals/
│           ├── index.ts              # Exportaciones centralizadas
│           ├── ModalBase.astro       # Componente base
│           ├── ModalServicio.astro   # Modal para servicios
│           ├── ModalProyecto.astro   # Modal para proyectos
│           ├── ModalFeature.astro    # Modal para características
│           └── ModalContacto.astro   # Modal de contacto
├── utils/
│   └── modal-utils.ts                # Utilidades y tipos
└── docs/
    └── MODAL_SYSTEM.md               # Esta documentación
```

## Convenciones de Nomenclatura

### IDs de Modales

Todos los modales deben seguir el patrón:

```
modal-{tipo}-{id}
```

**Ejemplos:**

-  `modal-servicio-podologia-general`
-  `modal-proyecto-clinica-madrid`
-  `modal-feature-sistema-citas`
-  `modal-contacto-principal`

### Tipos de Modales Soportados

-  `servicio`: Para mostrar detalles de servicios
-  `proyecto`: Para mostrar información de proyectos
-  `feature`: Para mostrar características/funcionalidades
-  `contacto`: Para formularios de contacto
-  `custom`: Para modales personalizados

## Cómo Usar el Sistema

### 1. Usar un Modal Existente

```astro
---
import { ModalServicio, generateModalId } from '../components/ui/modals';
import { getServicios } from '../data/servicios';

const servicios = getServicios();
---

<!-- Botón que abre el modal -->
<button data-modal-target={generateModalId('servicio', 'podologia-general')}>
  Ver servicio
</button>

<!-- Modal -->
<ModalServicio
  id={generateModalId('servicio', 'podologia-general')}
  servicio={servicioData}
/>
```

### 2. Abrir Modales Programáticamente

```javascript
// Abrir un modal específico
window.openModal("modal-servicio-podologia-general");

// Abrir con datos adicionales
window.openModal("modal-proyecto-clinica-madrid", {
	source: "homepage",
	campaign: "summer2024",
});

// Cerrar un modal específico
window.closeModal("modal-servicio-podologia-general");

// Cerrar todos los modales de un tipo
window.closeModalsByType("servicio");

// Verificar si hay modales abiertos de un tipo
if (window.hasOpenModalsOfType("proyecto")) {
	console.log("Hay proyectos abiertos");
}
```

## Cómo Crear un Nuevo Tipo de Modal

### Paso 1: Crear el Componente

Crea un nuevo archivo en `src/components/ui/modals/`:

```astro
---
// ModalMiTipo.astro
import ModalBase from "./ModalBase.astro";

export interface Props {
  id: string;
  miTipo: {
    titulo: string;
    descripcion: string;
    // ... otros campos específicos
  };
}

const { id, miTipo } = Astro.props;
---

<ModalBase id={id} title={miTipo.titulo} size="lg">
  <div class="space-y-6">
    <!-- Contenido específico del modal -->
    <p>{miTipo.descripcion}</p>

    <!-- CTA -->
    <div class="text-center">
      <button
        class="bg-primary-600 text-white px-6 py-3 rounded-xl"
        data-modal-target="citaModal"
        data-modal-close
      >
        Acción principal
      </button>
    </div>
  </div>
</ModalBase>
```

### Paso 2: Actualizar las Utilidades

Agrega el nuevo tipo en `src/utils/modal-utils.ts`:

```typescript
export type ModalType =
	| "servicio"
	| "proyecto"
	| "feature"
	| "contacto"
	| "miTipo"
	| "custom";

export const MODAL_CONFIGS: Record<ModalType, ModalConfig> = {
	// ... configuraciones existentes
	miTipo: {
		type: "miTipo",
		defaultSize: "md",
		closeOnBackdrop: true,
	},
};
```

### Paso 3: Actualizar las Exportaciones

En `src/components/ui/modals/index.ts`:

```typescript
// Agregar la exportación del componente
export { default as ModalMiTipo } from "./ModalMiTipo.astro";
export type { Props as ModalMiTipoProps } from "./ModalMiTipo.astro";

// Actualizar el mapa de componentes
export const MODAL_COMPONENTS = {
	// ... componentes existentes
	miTipo: "ModalMiTipo",
} as const;
```

### Paso 4: Registrar el Tipo en el ModalManager

En `src/components/scripts/modal-manager.js`, agregar:

```javascript
window.modalManager.registerModalType("miTipo", {
	defaultSize: "md",
	closeOnBackdrop: true,
});
```

### Paso 5: Usar el Nuevo Modal

```astro
---
import { ModalMiTipo, generateModalId } from '../components/ui/modals';
---

<button data-modal-target={generateModalId('miTipo', 'ejemplo-1')}>
  Abrir Mi Tipo
</button>

<ModalMiTipo
  id={generateModalId('miTipo', 'ejemplo-1')}
  miTipo={misDatos}
/>
```

## Eventos del Sistema

El sistema emite eventos personalizados que puedes escuchar:

```javascript
// Antes de abrir un modal
document.addEventListener("modalBeforeOpen", (event) => {
	const { modalId, type, elementId, data } = event.detail;
	console.log(`Abriendo modal ${type}: ${elementId}`);
});

// Cuando se abre un modal
document.addEventListener("modalOpen", (event) => {
	const { modalId, type, elementId } = event.detail;
	console.log(`Modal abierto: ${modalId}`);
});

// Cuando se cierra un modal
document.addEventListener("modalClose", (event) => {
	const { modalId, type, elementId } = event.detail;
	console.log(`Modal cerrado: ${modalId}`);
});
```

## Mejores Prácticas

### 1. Nomenclatura Consistente

-  Usa nombres descriptivos para los IDs
-  Mantén la convención `modal-{tipo}-{id}`
-  Usa kebab-case para los IDs

### 2. Tamaños de Modal

-  `sm`: Para confirmaciones simples
-  `md`: Para formularios básicos
-  `lg`: Para contenido detallado
-  `xl`: Para contenido extenso o galerías

### 3. Accesibilidad

-  Siempre incluye `aria-label` en botones de cierre
-  Usa títulos descriptivos
-  Mantén el foco en el modal cuando se abre

### 4. Rendimiento

-  Usa `generateModalId()` para consistencia
-  Evita crear múltiples instancias del mismo modal
-  Cierra modales no utilizados

## Ejemplos Completos

### Modal de Servicio

```astro
<ModalServicio
  id={generateModalId('servicio', 'podologia-general')}
  servicio={{
    titulo: "Podología General",
    descripcion: "Tratamiento completo...",
    precio: "50€",
    duracion: "45 min"
  }}
/>
```

### Modal de Proyecto

```astro
<ModalProyecto
  id={generateModalId('proyecto', 'clinica-madrid')}
  proyecto={{
    titulo: "Clínica Madrid",
    descripcion: "Proyecto de renovación...",
    cliente: "Clínica XYZ",
    estado: "completado"
  }}
/>
```

### Modal de Feature

```astro
<ModalFeature
  id={generateModalId('feature', 'sistema-citas')}
  feature={{
    titulo: "Sistema de Citas",
    descripcion: "Funcionalidad para...",
    categoria: "Gestión",
    estado: "estable"
  }}
/>
```

## Solución de Problemas

### Modal no se abre

1. Verifica que el ID sea correcto
2. Asegúrate de que el modal esté en el DOM
3. Revisa la consola por errores

### Estilos no se aplican

1. Verifica que ModalBase esté importado
2. Asegúrate de que las clases CSS estén disponibles
3. Revisa el z-index del modal

### Eventos no funcionan

1. Verifica que el modal-manager.js esté cargado
2. Asegúrate de usar los atributos `data-modal-target` correctos
3. Revisa que no haya conflictos con otros scripts

## Migración desde el Sistema Anterior

Si tienes modales existentes que usan el patrón anterior (`modal-{id}`), puedes migrarlos fácilmente:

### Antes:

```astro
<ModalServicio id="modal-podologia-general" />
<button data-modal-target="modal-podologia-general">Abrir</button>
```

### Después:

```astro
<ModalServicio id={generateModalId('servicio', 'podologia-general')} />
<button data-modal-target={generateModalId('servicio', 'podologia-general')}>Abrir</button>
```

## Changelog

### v2.0.0 - Sistema Extensible

-  ✅ Implementado patrón de IDs `modal-{tipo}-{id}`
-  ✅ Agregado ModalManager extensible con detección automática de tipos
-  ✅ Creados componentes ModalProyecto y ModalFeature como ejemplos
-  ✅ Implementadas utilidades TypeScript para generación de IDs
-  ✅ Agregadas funciones JavaScript para manejo por tipos
-  ✅ Documentación completa del sistema
-  ✅ Eventos personalizados con información de tipo
-  ✅ Registro dinámico de nuevos tipos de modales

### v1.0.0 - Sistema Básico

-  ✅ ModalManager básico
-  ✅ ModalBase, ModalServicio, ModalContacto
-  ✅ Gestión básica de apertura/cierre
