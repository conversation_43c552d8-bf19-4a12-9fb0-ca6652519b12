import { defineConfig } from "astro/config";
import path, { dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export default defineConfig({
	// site: "https://luinux81.github.io",
	// base: "podial",
	vite: {
		resolve: {
			alias: {
				"@/": `${path.resolve(__dirname, "src")}/`,
			},
		},
		css: {
			preprocessorOptions: {
				scss: {
					additionalData: `@import "@/styles/main.scss";`,
				},
			},
		},
	},
});
