# Arquitectura del Sistema de Páginas Dinámicas

## 🏗️ Estrategia de Diseño

### Principios Fundamentales

1. **Separación de Responsabilidades**
   - **Datos**: JSON para contenido, TypeScript para lógica
   - **Presentación**: Componentes Astro reutilizables
   - **Navegación**: Sistema dual (modales + páginas)

2. **Escalabilidad**
   - Estructura modular que permite agregar nuevos tipos de contenido
   - Componentes reutilizables con props configurables
   - Sistema de tipos TypeScript extensible

3. **Compatibilidad Retroactiva**
   - Mantiene el sistema de modales existente
   - No rompe funcionalidades previas
   - Migración gradual opcional

## 🎯 Decisiones Arquitectónicas

### 1. Generación Estática con Astro

**Decisión**: Usar `getStaticPaths()` para generar páginas en build time.

**Razones**:
- ✅ **Performance**: Páginas pre-renderizadas, carga instantánea
- ✅ **SEO**: HTML estático indexable por buscadores
- ✅ **Hosting**: Compatible con CDNs y hosting estático
- ✅ **Caching**: Cacheable a nivel de CDN

**Implementación**:
```typescript
export async function getStaticPaths() {
    const servicios = getServicios();
    return servicios.map((servicio) => ({
        params: { slug: servicio.id },
        props: { servicio },
    }));
}
```

### 2. Sistema de Datos JSON + TypeScript

**Decisión**: JSON para datos, TypeScript para validación y utilidades.

**Razones**:
- ✅ **Simplicidad**: Fácil edición de contenido sin código
- ✅ **Tipado**: Validación en tiempo de compilación
- ✅ **Reutilización**: Mismos datos para modales y páginas
- ✅ **Mantenibilidad**: Separación clara entre datos y lógica

**Estructura**:
```
data/
├── servicios.json    # Datos puros
├── servicios.ts      # Tipos + utilidades
├── equipo.json       # Datos puros
└── equipo.ts         # Tipos + utilidades
```

### 3. Layout Reutilizable

**Decisión**: Un layout común (`DetailLayout.astro`) para todas las páginas de detalle.

**Razones**:
- ✅ **Consistencia**: UX uniforme en todas las páginas
- ✅ **Mantenibilidad**: Cambios centralizados
- ✅ **DRY**: No repetir código de estructura
- ✅ **Flexibilidad**: Slots para contenido específico

**Características**:
- Breadcrumbs automáticos
- Hero section configurable
- Botón de retorno
- Meta tags SEO

### 4. Navegación Dual

**Decisión**: Permitir tanto modales como páginas completas.

**Razones**:
- ✅ **Flexibilidad**: Diferentes casos de uso
- ✅ **UX**: Usuarios pueden elegir su preferencia
- ✅ **Migración**: Transición gradual desde modales
- ✅ **SEO**: URLs indexables para páginas

**Implementación**:
```astro
<ServiceCard
    enableModal={true}          // Habilita modal
    enablePageNavigation={true} // Habilita página
/>
```

## 🔄 Flujo de Datos

### 1. Carga de Datos
```
JSON Files → TypeScript Utils → Astro Components → HTML
```

### 2. Generación de Rutas
```
getStaticPaths() → Build Time → Static HTML Files
```

### 3. Navegación
```
User Click → Modal OR Page → Content Display
```

## 🧩 Componentes Clave

### 1. DetailLayout.astro
- **Propósito**: Layout base para páginas de detalle
- **Props**: `title`, `description`, `image`, `breadcrumbs`
- **Características**: SEO, breadcrumbs, responsive

### 2. ServiceCard.astro / EquipoCard.astro / ProyectoCard.astro
- **Propósito**: Tarjetas con navegación dual
- **Props**: Datos del elemento + flags de navegación
- **Características**: Hover effects, botones condicionales

### 3. Utilidades TypeScript
- **Propósito**: Funciones helper y validación
- **Funciones**: `getServicios()`, `getMiembroPorId()`, etc.
- **Tipos**: Interfaces TypeScript para type safety

## 📊 Patrones de Diseño Utilizados

### 1. **Factory Pattern**
- Generación automática de páginas basada en datos
- Mismo template, diferentes datos

### 2. **Strategy Pattern**
- Navegación dual: modal vs página
- Componentes configurables via props

### 3. **Template Method Pattern**
- DetailLayout como template base
- Slots para contenido específico

### 4. **Data Access Object (DAO)**
- Funciones TypeScript como capa de acceso a datos
- Abstracción sobre archivos JSON

## 🔧 Extensibilidad

### Agregar Nuevo Tipo de Contenido

1. **Crear datos**: `src/data/nuevo-tipo.json`
2. **Crear utilidades**: `src/data/nuevo-tipo.ts`
3. **Crear componente**: `src/components/NuevoTipoCard.astro`
4. **Crear páginas**: `src/pages/nuevo-tipo/[slug].astro`

### Personalizar Layout

1. **Extender DetailLayout**: Agregar nuevas props
2. **Crear layout específico**: Para casos especiales
3. **Modificar estilos**: Via Tailwind CSS

## 🚀 Optimizaciones Implementadas

### Performance
- **Static Generation**: Páginas pre-renderizadas
- **Image Optimization**: Lazy loading automático
- **CSS Purging**: Solo CSS usado se incluye

### SEO
- **Meta Tags**: Título y descripción únicos
- **Structured Data**: Breadcrumbs semánticos
- **URLs Amigables**: `/servicios/quiropodologia`

### UX
- **Responsive Design**: Mobile-first approach
- **Loading States**: Transiciones suaves
- **Accessibility**: ARIA labels y navegación por teclado

## 📈 Métricas y Monitoreo

### Build Time
- Páginas generadas automáticamente
- Tiempo de build proporcional al contenido

### Runtime
- Navegación instantánea (páginas estáticas)
- Carga diferida de imágenes

### Mantenimiento
- Cambios de contenido solo requieren rebuild
- Estructura de código modular y testeable

---

**Nota para Desarrolladores**: Esta arquitectura prioriza la simplicidad y mantenibilidad sobre la complejidad. Cada decisión está orientada a facilitar el desarrollo futuro y la gestión de contenido.
