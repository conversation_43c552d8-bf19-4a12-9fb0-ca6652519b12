# ImageGallery Component

Componente reutilizable para mostrar galerías de imágenes con funcionalidad de lightbox completamente personalizable.

## Características

- ✅ Galería responsive con grid adaptativo
- ✅ Lightbox a pantalla completa con navegación
- ✅ Completamente personalizable mediante clases CSS
- ✅ Navegación por teclado (ESC, flechas)
- ✅ Soporte para múltiples galerías en la misma página
- ✅ Tamaños responsive automáticos
- ✅ Accesibilidad integrada

## Props Básicas

```typescript
interface Props {
  imagenes: string[];           // Array de URLs de imágenes
  titulo: string;              // Título para alt text
  showTitle?: boolean;         // Mostrar título (default: true)
  enableLightbox?: boolean;    // Habilitar lightbox (default: true)
}
```

## Props de Personalización

```typescript
interface CustomizationProps {
  class?: string;              // Contenedor principal
  overlayClass?: string;       // Overlay del lightbox
  imageClass?: string;         // Imágenes de la galería
  lightboxImageClass?: string; // Imagen del lightbox
  closeButtonClass?: string;   // Botón de cerrar
  navButtonClass?: string;     // Botones de navegación
  counterClass?: string;       // Contador de imágenes
}
```

## Uso Básico

```astro
<ImageGallery 
  imagenes={["img1.jpg", "img2.jpg", "img3.jpg"]} 
  titulo="Mi Galería" 
/>
```

## Ejemplos de Personalización

### Overlay Personalizado
```astro
<ImageGallery 
  imagenes={imagenes} 
  titulo="Galería" 
  overlayClass="!bg-blue-900 !bg-opacity-95"
/>
```

### Imágenes con Estilo Personalizado
```astro
<ImageGallery 
  imagenes={imagenes} 
  titulo="Galería" 
  imageClass="!rounded-xl border-4 border-primary-200 shadow-xl !h-32"
/>
```

### Botones Personalizados
```astro
<ImageGallery 
  imagenes={imagenes} 
  titulo="Galería" 
  closeButtonClass="!bg-red-600 hover:!bg-red-700 !w-16 !h-16"
  navButtonClass="!bg-purple-600 hover:!bg-purple-700 !rounded-lg"
/>
```

### Tema Completo Personalizado
```astro
<ImageGallery 
  imagenes={imagenes} 
  titulo="Tema Oscuro" 
  class="bg-gray-900 p-6 rounded-xl"
  overlayClass="!bg-gray-900 !bg-opacity-98"
  imageClass="!rounded-2xl border-2 border-gray-600 hover:border-yellow-400"
  closeButtonClass="!bg-red-600 hover:!bg-red-700 border-2 border-white"
  navButtonClass="!bg-yellow-500 hover:!bg-yellow-600 !text-black"
  lightboxImageClass="border-4 border-yellow-400 rounded-xl"
  counterClass="!bg-yellow-500 !text-black !px-4 !py-2 font-bold"
/>
```

## Tamaños Responsive por Defecto

| Breakpoint | Tamaño de Imagen |
|------------|------------------|
| Móvil      | 90% del viewport |
| SM (640px+)| 85% del viewport |
| MD (768px+)| 80% del viewport |
| LG (1024px+)| 75% del viewport |
| XL (1280px+)| 70% del viewport |

## Controles del Lightbox

- **Click en imagen**: Abre el lightbox
- **ESC**: Cierra el lightbox
- **Flechas ← →**: Navega entre imágenes
- **Click en overlay**: Cierra el lightbox
- **Botón cerrar**: Cierra el lightbox

## Consejos de Personalización

1. **Usar `!important`**: Prefija las clases con `!` para sobrescribir estilos por defecto
2. **Combinar clases**: Separa múltiples clases con espacios
3. **Responsive**: Usa prefijos de Tailwind como `md:`, `lg:`, etc.
4. **Estados hover**: Usa `hover:` para efectos interactivos

## Estructura de Clases por Defecto

```css
/* Contenedor principal */
.gallery-container { /* class prop */ }

/* Overlay del lightbox */
.lightbox-overlay { 
  @apply fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center;
  /* + overlayClass */
}

/* Imágenes de la galería */
.gallery-image { 
  @apply rounded-lg object-cover h-24 w-full hover:scale-105 transition-transform cursor-pointer;
  /* + imageClass */
}

/* Imagen del lightbox */
.lightbox-image { 
  @apply w-[90%] h-[90%] sm:w-[85%] sm:h-[85%] md:w-[80%] md:h-[80%] lg:w-[75%] lg:h-[75%] xl:w-[70%] xl:h-[70%] object-contain;
  /* + lightboxImageClass */
}

/* Botón de cerrar */
.close-button { 
  @apply absolute top-4 right-4 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full w-12 h-12 flex items-center justify-center transition-all duration-200 z-20 hover:scale-110;
  /* + closeButtonClass */
}

/* Botones de navegación */
.nav-button { 
  @apply absolute top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full w-10 h-10 md:w-12 md:h-12 flex items-center justify-center transition-all duration-200 z-10 hover:scale-110;
  /* + navButtonClass */
}

/* Contador */
.counter { 
  @apply absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm md:text-base;
  /* + counterClass */
}
```
