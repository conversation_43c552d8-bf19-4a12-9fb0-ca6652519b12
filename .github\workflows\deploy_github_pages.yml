name: Deploy to GitHub Pages

on:
   # Activa el flujo de trabajo cada vez que hagas push a la rama `main`
   # Usando un nombre de rama diferente? Reemplaza `main` con el nombre de tu rama
   push:
      branches: [deployment_github_pages]
   # Te permite ejecutar este flujo de trabajo manualmente desde la pestaña Acciones en GitHub.
   workflow_dispatch:

# Permite que este trabajo clone el repositorio y cree un despliegue de página
permissions:
   contents: read
   pages: write
   id-token: write

jobs:
   build:
      runs-on: ubuntu-latest
      steps:
         - name: Checkout your repository using git
           uses: actions/checkout@v4
         - name: Install, build, and upload your site
           uses: withastro/action@v3
           # with:
           # path: . # La ubicación raíz de tu proyecto de Astro dentro del repositorio. (opcional)
           # node-version: 20 # La versión específica de Node que debería usarse para construir tu sitio. Por defecto es 20. (opcional)
           # package-manager: pnpm@latest # El gestor de paquetes de Node que debería usarse para instalar dependencias y construir tu sitio. Detectado automáticamente basado en tu lockfile. (opcional)

   deploy:
      needs: build
      runs-on: ubuntu-latest
      environment:
         name: github-pages
         url: ${{ steps.deployment.outputs.page_url }}
      steps:
         - name: Deploy to GitHub Pages
           id: deployment
           uses: actions/deploy-pages@v4
