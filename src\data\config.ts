/**
 * Configuración del sistema de servicios
 * 
 * Este archivo permite personalizar el comportamiento del sistema de modales
 * y servicios sin modificar el código de los componentes.
 */

export interface ServiciosConfig {
  // Configuración de la sección de servicios
  seccion: {
    titulo: string;
    descripcion: string;
    mostrarEnGrid: boolean;
    columnasGrid: {
      mobile: number;
      tablet: number;
      desktop: number;
    };
  };
  
  // Configuración de los modales
  modal: {
    tamaño: 'sm' | 'md' | 'lg' | 'xl';
    mostrarIconoEnTitulo: boolean;
    mostrarBotonCita: boolean;
    textoBotonCita: string;
    targetModalCita: string;
  };
  
  // Configuración de las tarjetas de servicio
  tarjeta: {
    mostrarBotonDetalle: boolean;
    textoBotonDetalle: string;
    alturaImagen: string;
    efectoHover: boolean;
  };
  
  // Configuración de validación
  validacion: {
    validarEstructura: boolean;
    mostrarErroresConsola: boolean;
    camposObligatorios: string[];
  };
}

// Configuración por defecto
export const configPorDefecto: ServiciosConfig = {
  seccion: {
    titulo: "Nuestros Servicios",
    descripcion: "Ofrecemos una amplia gama de tratamientos podológicos para todas las edades y necesidades",
    mostrarEnGrid: true,
    columnasGrid: {
      mobile: 1,
      tablet: 2,
      desktop: 3
    }
  },
  
  modal: {
    tamaño: 'lg',
    mostrarIconoEnTitulo: true,
    mostrarBotonCita: true,
    textoBotonCita: "Pedir cita ahora",
    targetModalCita: "citaModal"
  },
  
  tarjeta: {
    mostrarBotonDetalle: true,
    textoBotonDetalle: "Ver más detalles",
    alturaImagen: "h-48",
    efectoHover: true
  },
  
  validacion: {
    validarEstructura: true,
    mostrarErroresConsola: true,
    camposObligatorios: [
      'id',
      'image', 
      'title',
      'description',
      'alt',
      'servicio.titulo',
      'servicio.descripcion',
      'servicio.icono'
    ]
  }
};

// Configuración personalizada (modifica según tus necesidades)
export const config: ServiciosConfig = {
  ...configPorDefecto,
  
  // Ejemplo de personalización:
  // seccion: {
  //   ...configPorDefecto.seccion,
  //   titulo: "Mis Servicios Personalizados",
  //   descripcion: "Descripción personalizada de mis servicios"
  // },
  
  // modal: {
  //   ...configPorDefecto.modal,
  //   tamaño: 'xl',
  //   textoBotonCita: "Contactar ahora"
  // }
};

/**
 * Obtiene la configuración actual del sistema
 * @returns Configuración activa
 */
export function getConfig(): ServiciosConfig {
  return config;
}

/**
 * Obtiene una configuración específica por clave
 * @param clave - Clave de configuración (ej: 'modal.tamaño')
 * @returns Valor de la configuración
 */
export function getConfigValue(clave: string): any {
  const claves = clave.split('.');
  let valor: any = config;
  
  for (const k of claves) {
    if (valor && typeof valor === 'object' && k in valor) {
      valor = valor[k];
    } else {
      return undefined;
    }
  }
  
  return valor;
}

/**
 * Valida que la configuración tenga la estructura correcta
 * @param configuracion - Configuración a validar
 * @returns true si es válida
 */
export function validarConfig(configuracion: any): configuracion is ServiciosConfig {
  return (
    configuracion &&
    typeof configuracion === 'object' &&
    configuracion.seccion &&
    configuracion.modal &&
    configuracion.tarjeta &&
    configuracion.validacion
  );
}

// Exportar configuración por defecto para referencia
export default config;
