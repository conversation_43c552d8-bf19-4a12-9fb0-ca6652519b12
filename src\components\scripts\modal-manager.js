// Sistema de gestión de modales optimizado y extensible
class ModalManager {
	constructor() {
		this.openModals = new Set();
		this.listenersInitialized = false;
		this.modalTypes = new Map(); // Almacena información sobre tipos de modales

		// Solo inicializar si estamos en el cliente
		if (typeof document !== "undefined") {
			this.init();
		}
	}

	init() {
		// Verificar si el DOM ya está cargado
		if (document.readyState === "loading") {
			document.addEventListener("DOMContentLoaded", () => {
				this.setupEventListeners();
			});
		} else {
			// DOM ya está cargado
			this.setupEventListeners();
		}
	}

	setupEventListeners() {
		// Evitar duplicar listeners
		if (this.listenersInitialized) {
			return;
		}

		// Usar delegación de eventos para mejor rendimiento
		// y evitar problemas con elementos dinámicos
		document.addEventListener("click", (e) => {
			// Manejar apertura de modales
			const target = e.target.closest("[data-modal-target]");
			if (target) {
				e.preventDefault();
				e.stopPropagation();
				const modalId = target.getAttribute("data-modal-target");
				if (modalId) {
					this.openModal(modalId);
				}
				return;
			}

			// Manejar cierre de modales
			const closeTarget = e.target.closest("[data-modal-close]");
			if (closeTarget) {
				e.preventDefault();
				e.stopPropagation();
				const modal =
					closeTarget.closest('[id*="modal"], [id*="Modal"]') ||
					closeTarget.closest(".fixed");
				if (modal) {
					this.closeModal(modal.id);
				}
				return;
			}

			// Cerrar modal al hacer click en el backdrop
			const modalBackdrop = e.target;
			if (
				modalBackdrop.classList.contains("fixed") &&
				modalBackdrop.id &&
				modalBackdrop.id.includes("modal")
			) {
				// Solo cerrar si el click fue directamente en el backdrop
				if (e.target === modalBackdrop) {
					this.closeModal(modalBackdrop.id);
				}
			}
		});

		// Cerrar modales con Escape
		document.addEventListener("keydown", (e) => {
			if (e.key === "Escape") {
				this.closeTopModal();
			}
		});

		this.listenersInitialized = true;
	}

	/**
	 * Extrae información del tipo de modal basándose en el ID
	 * @param {string} modalId - ID del modal en formato modal-{tipo}-{id}
	 * @returns {Object|null} Información del modal o null si no es válido
	 */
	parseModalId(modalId) {
		const match = modalId.match(/^modal-([^-]+)-(.+)$/);
		if (!match) return null;

		const [, type, id] = match;
		return { type, id, modalId };
	}

	/**
	 * Registra un tipo de modal con su configuración
	 * @param {string} type - Tipo de modal
	 * @param {Object} config - Configuración del tipo de modal
	 */
	registerModalType(type, config = {}) {
		this.modalTypes.set(type, {
			defaultSize: "md",
			closeOnBackdrop: true,
			...config,
		});
	}

	openModal(modalId) {
		const modal = document.getElementById(modalId);
		if (!modal) {
			console.warn(`Modal con id "${modalId}" no encontrado`);
			return;
		}

		// Extraer información del tipo de modal
		const modalInfo = this.parseModalId(modalId);

		// Aplicar z-index escalonado para modales superpuestos
		const zIndex = 50 + this.openModals.size * 10;
		modal.style.zIndex = zIndex;

		modal.classList.remove("hidden");
		modal.classList.add("flex");

		this.openModals.add(modalId);
		this.updateBodyScroll();

		// Enfocar el modal para accesibilidad
		const focusableElement = modal.querySelector(
			'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
		);
		if (focusableElement) {
			focusableElement.focus();
		}

		// Emitir evento personalizado con información del tipo
		const event = new CustomEvent("modalOpen", {
			detail: {
				modalId,
				modal,
				type: modalInfo?.type,
				elementId: modalInfo?.id,
				modalInfo,
			},
		});
		document.dispatchEvent(event);
	}

	closeModal(modalId) {
		const modal = document.getElementById(modalId);
		if (!modal) return;

		// Extraer información del tipo de modal
		const modalInfo = this.parseModalId(modalId);

		modal.classList.add("hidden");
		modal.classList.remove("flex");

		// Resetear z-index
		modal.style.zIndex = "";

		this.openModals.delete(modalId);
		this.updateBodyScroll();

		// Emitir evento personalizado con información del tipo
		const event = new CustomEvent("modalClose", {
			detail: {
				modalId,
				modal,
				type: modalInfo?.type,
				elementId: modalInfo?.id,
				modalInfo,
			},
		});
		document.dispatchEvent(event);
	}

	closeTopModal() {
		if (this.openModals.size === 0) return;

		const modals = Array.from(this.openModals);
		const topModalId = modals[modals.length - 1];
		this.closeModal(topModalId);
	}

	closeAllModals() {
		Array.from(this.openModals).forEach((modalId) => {
			this.closeModal(modalId);
		});
	}

	updateBodyScroll() {
		if (this.openModals.size > 0) {
			document.body.style.overflow = "hidden";
		} else {
			document.body.style.overflow = "";
		}
	}

	// Métodos públicos para uso programático
	isModalOpen(modalId) {
		return this.openModals.has(modalId);
	}

	getOpenModals() {
		return Array.from(this.openModals);
	}

	// Método para abrir modal con datos dinámicos
	openModalWithData(modalId, data = {}) {
		const modal = document.getElementById(modalId);
		if (!modal) return;

		// Extraer información del tipo de modal
		const modalInfo = this.parseModalId(modalId);

		// Permitir personalización del modal antes de abrirlo
		const event = new CustomEvent("modalBeforeOpen", {
			detail: {
				modalId,
				modal,
				data,
				type: modalInfo?.type,
				elementId: modalInfo?.id,
				modalInfo,
			},
		});
		document.dispatchEvent(event);

		this.openModal(modalId);
	}

	/**
	 * Obtiene todos los modales abiertos de un tipo específico
	 * @param {string} type - Tipo de modal
	 * @returns {Array} Array de IDs de modales del tipo especificado
	 */
	getOpenModalsByType(type) {
		return Array.from(this.openModals).filter((modalId) => {
			const modalInfo = this.parseModalId(modalId);
			return modalInfo?.type === type;
		});
	}

	/**
	 * Cierra todos los modales de un tipo específico
	 * @param {string} type - Tipo de modal a cerrar
	 */
	closeModalsByType(type) {
		const modalsToClose = this.getOpenModalsByType(type);
		modalsToClose.forEach((modalId) => this.closeModal(modalId));
	}

	/**
	 * Verifica si hay modales abiertos de un tipo específico
	 * @param {string} type - Tipo de modal
	 * @returns {boolean} true si hay modales del tipo abiertos
	 */
	hasOpenModalsOfType(type) {
		return this.getOpenModalsByType(type).length > 0;
	}

	/**
	 * Obtiene información sobre todos los modales abiertos
	 * @returns {Array} Array con información detallada de cada modal abierto
	 */
	getDetailedOpenModals() {
		return Array.from(this.openModals).map((modalId) => {
			const modalInfo = this.parseModalId(modalId);
			const modal = document.getElementById(modalId);
			return {
				modalId,
				modal,
				...modalInfo,
			};
		});
	}
}

// Solo inicializar si estamos en el cliente
if (typeof window !== "undefined") {
	// Evitar múltiples instancias
	if (!window.modalManager) {
		// Inicializar el sistema de modales
		window.modalManager = new ModalManager();

		// Registrar tipos de modales predeterminados
		window.modalManager.registerModalType("servicio", {
			defaultSize: "lg",
			closeOnBackdrop: true,
		});

		window.modalManager.registerModalType("proyecto", {
			defaultSize: "xl",
			closeOnBackdrop: true,
		});

		window.modalManager.registerModalType("feature", {
			defaultSize: "md",
			closeOnBackdrop: true,
		});

		window.modalManager.registerModalType("contacto", {
			defaultSize: "md",
			closeOnBackdrop: true,
		});

		// Funciones helper para compatibilidad
		window.openModal = (modalId, data) =>
			window.modalManager.openModalWithData(modalId, data);
		window.closeModal = (modalId) => window.modalManager.closeModal(modalId);
		window.closeAllModals = () => window.modalManager.closeAllModals();

		// Funciones helper adicionales para el sistema extensible
		window.closeModalsByType = (type) =>
			window.modalManager.closeModalsByType(type);
		window.getOpenModalsByType = (type) =>
			window.modalManager.getOpenModalsByType(type);
		window.hasOpenModalsOfType = (type) =>
			window.modalManager.hasOpenModalsOfType(type);
		window.registerModalType = (type, config) =>
			window.modalManager.registerModalType(type, config);
	}
}
