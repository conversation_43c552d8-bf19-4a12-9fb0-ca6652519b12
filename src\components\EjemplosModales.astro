---
/**
 * EjemplosModales - Componente de demostración del sistema extensible de modales
 * 
 * Este componente muestra cómo usar los diferentes tipos de modales
 * disponibles en el sistema extensible.
 */
import { ModalProyecto, ModalFeature, generateModalId } from "./ui/modals";

// Datos de ejemplo para proyectos
const proyectosEjemplo = [
  {
    id: "clinica-madrid",
    title: "Clínica Madrid",
    description: "Renovación completa de clínica podológica",
    proyecto: {
      titulo: "Clínica Podológica Madrid",
      descripcion: "Proyecto integral de renovación y modernización de una clínica podológica ubicada en el centro de Madrid.",
      cliente: "Clínica Podal Madrid S.L.",
      fecha: "Marzo 2024",
      ubicacion: "Madrid, España",
      estado: "completado",
      presupuesto: "€85,000",
      duracion: "3 meses",
      tecnologias: ["Equipos láser", "Sistema de gestión digital", "Mobiliario ergonómico"],
      caracteristicas: [
        "Renovación completa de 4 consultas",
        "Sala de espera moderna y cómoda",
        "Sistema de gestión de citas digital"
      ],
      resultados: [
        "Aumento del 40% en la satisfacción del paciente",
        "Reducción del 30% en tiempos de espera"
      ]
    }
  }
];

// Datos de ejemplo para features
const featuresEjemplo = [
  {
    id: "sistema-citas",
    title: "Sistema de Citas",
    description: "Plataforma digital para gestión de citas",
    feature: {
      titulo: "Sistema de Citas Online",
      descripcion: "Plataforma digital completa que permite a los pacientes reservar, modificar y cancelar citas médicas de forma autónoma.",
      categoria: "Gestión Digital",
      version: "2.1.0",
      estado: "estable",
      prioridad: "alta",
      beneficios: [
        "Disponibilidad 24/7 para reservar citas",
        "Recordatorios automáticos por SMS y email",
        "Reducción de no-shows hasta en un 40%"
      ],
      pasos: [
        "Acceder al portal web de la clínica",
        "Iniciar sesión con sus credenciales",
        "Seleccionar el tipo de consulta deseada",
        "Elegir fecha y hora disponible"
      ]
    }
  }
];
---

<section class="py-16 bg-white">
  <div class="container mx-auto px-4">
    <div class="text-center mb-12">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
        Sistema de Modales Extensible
      </h2>
      <p class="text-lg text-gray-600 max-w-3xl mx-auto">
        Ejemplos de diferentes tipos de modales disponibles en el sistema
      </p>
    </div>

    <!-- Grid de ejemplos -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      
      <!-- Ejemplo Modal Proyecto -->
      <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
        <div class="text-center">
          <div class="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <span class="material-symbols-outlined text-2xl">business</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">Modal Proyecto</h3>
          <p class="text-gray-600 mb-4">
            Muestra información detallada de proyectos realizados
          </p>
          <button
            data-modal-target={generateModalId('proyecto', 'clinica-madrid')}
            class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Ver Ejemplo
          </button>
        </div>
      </div>

      <!-- Ejemplo Modal Feature -->
      <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
        <div class="text-center">
          <div class="bg-green-600 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <span class="material-symbols-outlined text-2xl">star</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">Modal Feature</h3>
          <p class="text-gray-600 mb-4">
            Presenta características y funcionalidades del sistema
          </p>
          <button
            data-modal-target={generateModalId('feature', 'sistema-citas')}
            class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            Ver Ejemplo
          </button>
        </div>
      </div>

      <!-- Ejemplo Modal Servicio (existente) -->
      <div class="bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-6 border border-purple-100">
        <div class="text-center">
          <div class="bg-purple-600 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <span class="material-symbols-outlined text-2xl">medical_services</span>
          </div>
          <h3 class="text-xl font-semibold text-gray-800 mb-2">Modal Servicio</h3>
          <p class="text-gray-600 mb-4">
            Muestra detalles de servicios médicos disponibles
          </p>
          <a
            href="#servicios"
            class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors inline-block"
          >
            Ver en Servicios
          </a>
        </div>
      </div>

    </div>

    <!-- Información técnica -->
    <div class="mt-16 bg-gray-50 rounded-xl p-8">
      <h3 class="text-2xl font-semibold text-gray-800 mb-6 text-center">
        Información Técnica
      </h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        
        <!-- Patrón de IDs -->
        <div>
          <h4 class="text-lg font-semibold text-gray-700 mb-3 flex items-center">
            <span class="material-symbols-outlined text-blue-600 mr-2">tag</span>
            Patrón de IDs
          </h4>
          <div class="bg-white rounded-lg p-4 border">
            <code class="text-sm text-gray-800">
              modal-{tipo}-{id}
            </code>
            <div class="mt-2 text-sm text-gray-600">
              <p><strong>Ejemplos:</strong></p>
              <ul class="list-disc list-inside mt-1 space-y-1">
                <li>modal-servicio-podologia-general</li>
                <li>modal-proyecto-clinica-madrid</li>
                <li>modal-feature-sistema-citas</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Funciones JavaScript -->
        <div>
          <h4 class="text-lg font-semibold text-gray-700 mb-3 flex items-center">
            <span class="material-symbols-outlined text-green-600 mr-2">code</span>
            Funciones Disponibles
          </h4>
          <div class="bg-white rounded-lg p-4 border">
            <div class="text-sm text-gray-800 space-y-2">
              <div><code>openModal(modalId)</code></div>
              <div><code>closeModal(modalId)</code></div>
              <div><code>closeModalsByType(type)</code></div>
              <div><code>hasOpenModalsOfType(type)</code></div>
              <div><code>registerModalType(type, config)</code></div>
            </div>
          </div>
        </div>

      </div>

      <!-- Botones de demostración -->
      <div class="mt-8 text-center">
        <h4 class="text-lg font-semibold text-gray-700 mb-4">
          Prueba las Funciones JavaScript
        </h4>
        <div class="flex flex-wrap justify-center gap-4">
          <button
            onclick="window.openModal('modal-proyecto-clinica-madrid')"
            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
          >
            Abrir Modal Proyecto
          </button>
          <button
            onclick="window.closeModalsByType('proyecto')"
            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm"
          >
            Cerrar Modales Proyecto
          </button>
          <button
            onclick="alert('Modales de proyecto abiertos: ' + window.getOpenModalsByType('proyecto').length)"
            class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-sm"
          >
            Contar Modales Proyecto
          </button>
        </div>
      </div>

    </div>
  </div>

  <!-- Modales de ejemplo -->
  {proyectosEjemplo.map((proyecto) => (
    <ModalProyecto
      id={generateModalId('proyecto', proyecto.id)}
      proyecto={proyecto.proyecto}
    />
  ))}

  {featuresEjemplo.map((feature) => (
    <ModalFeature
      id={generateModalId('feature', feature.id)}
      feature={feature.feature}
    />
  ))}

</section>
