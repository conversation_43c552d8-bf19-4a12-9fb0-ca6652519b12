# API Reference - Sistema de Páginas Dinámicas

## 📚 Funciones y Tipos TypeScript

Esta referencia documenta todas las funciones, interfaces y utilidades disponibles en el sistema.

## 🔧 Servicios (`src/data/servicios.ts`)

### Interfaces

#### `ServicioDetalle`
```typescript
interface ServicioDetalle {
  titulo: string;           // Título completo del servicio
  descripcion: string;      // Descripción detallada
  icono: string;           // Nombre del icono Material Symbols
  precio?: string;         // Información de precios (opcional)
  duracion?: string;       // Duración del tratamiento (opcional)
  beneficios?: string[];   // Lista de beneficios (opcional)
  proceso?: string[];      // Pasos del proceso (opcional)
  imagenes?: string[];     // URLs de imágenes adicionales (opcional)
}
```

#### `Servicio`
```typescript
interface Servicio {
  id: string;              // Identificador único
  image: string;           // URL de la imagen principal
  title: string;           // Título para la tarjeta
  description: string;     // Descripción breve
  alt: string;            // Texto alternativo de la imagen
  servicio: ServicioDetalle; // Información detallada
}
```

### Funciones

#### `getServicios(): Servicio[]`
Obtiene todos los servicios desde el archivo JSON.

**Retorna**: Array de servicios válidos
**Ejemplo**:
```typescript
const servicios = getServicios();
console.log(`Total servicios: ${servicios.length}`);
```

#### `getServicioPorId(id: string): Servicio | undefined`
Busca un servicio específico por su ID.

**Parámetros**:
- `id`: Identificador del servicio

**Retorna**: Servicio encontrado o `undefined`
**Ejemplo**:
```typescript
const servicio = getServicioPorId('quiropodologia');
if (servicio) {
  console.log(servicio.title);
}
```

#### `validarServicio(servicio: any): servicio is Servicio`
Valida la estructura de un objeto servicio.

**Parámetros**:
- `servicio`: Objeto a validar

**Retorna**: `true` si la estructura es válida
**Ejemplo**:
```typescript
if (validarServicio(data)) {
  // data es un Servicio válido
  procesarServicio(data);
}
```

## 👥 Equipo (`src/data/equipo.ts`)

### Interfaces

#### `MiembroDetalle`
```typescript
interface MiembroDetalle {
  nombre: string;              // Nombre completo
  cargo: string;               // Cargo detallado
  descripcion: string;         // Descripción del miembro
  experiencia: string;         // Años de experiencia
  especialidades?: string[];   // Lista de especialidades (opcional)
  formacion?: string[];        // Formación académica (opcional)
  certificaciones?: string[];  // Certificaciones (opcional)
  idiomas?: string[];          // Idiomas (opcional)
  horarios?: string;           // Horarios de atención (opcional)
  telefono?: string;           // Teléfono de contacto (opcional)
  email?: string;              // Email de contacto (opcional)
  linkedin?: string;           // URL de LinkedIn (opcional)
  imagenes?: string[];         // Imágenes adicionales (opcional)
}
```

#### `Miembro`
```typescript
interface Miembro {
  id: string;                  // Identificador único
  image: string;               // URL de la imagen principal
  name: string;                // Nombre para la tarjeta
  position: string;            // Posición para la tarjeta
  description: string;         // Descripción breve
  alt: string;                // Texto alternativo
  miembro: MiembroDetalle;    // Información detallada
}
```

### Funciones

#### `getEquipo(): Miembro[]`
Obtiene todos los miembros del equipo.

#### `getMiembroPorId(id: string): Miembro | undefined`
Busca un miembro específico por ID.

#### `validarMiembro(miembro: any): miembro is Miembro`
Valida la estructura de un miembro.

#### `getEquipoIds(): string[]`
Obtiene todos los IDs de miembros para generación de rutas.

## 🏗️ Proyectos (`src/data/proyectos.ts`)

### Interfaces

#### `ProyectoDetalle`
```typescript
interface ProyectoDetalle {
  titulo: string;                    // Título completo
  descripcion: string;               // Descripción detallada
  cliente?: string;                  // Nombre del cliente (opcional)
  fecha?: string;                    // Fecha del proyecto (opcional)
  ubicacion?: string;                // Ubicación (opcional)
  estado?: 'planificacion' | 'en-progreso' | 'completado' | 'pausado';
  presupuesto?: string;              // Presupuesto (opcional)
  duracion?: string;                 // Duración (opcional)
  tecnologias?: string[];            // Tecnologías utilizadas (opcional)
  caracteristicas?: string[];        // Características (opcional)
  equipo?: string[];                 // Miembros del equipo (opcional)
  resultados?: string[];             // Resultados obtenidos (opcional)
  imagenes?: string[];               // Galería de imágenes (opcional)
}
```

#### `Proyecto`
```typescript
interface Proyecto {
  id: string;                        // Identificador único
  title: string;                     // Título para la tarjeta
  description: string;               // Descripción breve
  image: string;                     // URL de la imagen principal
  alt: string;                      // Texto alternativo
  proyecto: ProyectoDetalle;        // Información detallada
}
```

### Funciones

#### `getProyectos(): Proyecto[]`
Obtiene todos los proyectos.

#### `getProyectoPorId(id: string): Proyecto | undefined`
Busca un proyecto específico por ID.

#### `getProyectosPorEstado(estado: string): Proyecto[]`
Filtra proyectos por estado.

**Ejemplo**:
```typescript
const completados = getProyectosPorEstado('completado');
const enProgreso = getProyectosPorEstado('en-progreso');
```

#### `getEstadisticasProyectos()`
Obtiene estadísticas de proyectos.

**Retorna**:
```typescript
{
  total: number;                    // Total de proyectos
  completados: number;              // Proyectos completados
  enProgreso: number;               // Proyectos en progreso
  planificacion: number;            // Proyectos en planificación
  porcentajeCompletados: number;    // Porcentaje de éxito
}
```

## 🎨 Componentes

### ServiceCard Props

```typescript
interface ServiceCardProps {
  image: string;                    // URL de la imagen
  title: string;                    // Título del servicio
  description: string;              // Descripción breve
  alt: string;                     // Texto alternativo
  servicioId: string;              // ID del servicio
  enableModal?: boolean;           // Habilitar modal (default: true)
  enablePageNavigation?: boolean;  // Habilitar página (default: true)
}
```

### EquipoCard Props

```typescript
interface EquipoCardProps {
  image: string;                    // URL de la imagen
  name: string;                     // Nombre del miembro
  position: string;                 // Posición/cargo
  description: string;              // Descripción breve
  alt: string;                     // Texto alternativo
  miembroId: string;               // ID del miembro
  enableModal?: boolean;           // Habilitar modal (default: false)
  enablePageNavigation?: boolean;  // Habilitar página (default: true)
}
```

### ProyectoCard Props

```typescript
interface ProyectoCardProps {
  image: string;                    // URL de la imagen
  title: string;                    // Título del proyecto
  description: string;              // Descripción breve
  alt: string;                     // Texto alternativo
  proyectoId: string;              // ID del proyecto
  estado?: string;                 // Estado del proyecto (opcional)
  fecha?: string;                  // Fecha del proyecto (opcional)
  enableModal?: boolean;           // Habilitar modal (default: false)
  enablePageNavigation?: boolean;  // Habilitar página (default: true)
}
```

### DetailLayout Props

```typescript
interface DetailLayoutProps {
  title: string;                    // Título de la página
  description: string;              // Descripción para SEO
  image?: string;                  // Imagen hero (opcional)
  breadcrumbs?: Array<{            // Breadcrumbs (opcional)
    name: string;
    href: string;
  }>;
}
```

## 🔍 Funciones de Utilidad

### Validación de Datos

Todas las funciones de validación siguen el mismo patrón:

```typescript
function validarElemento(elemento: any): elemento is TipoElemento {
  // Validación de campos requeridos
  // Retorna true si es válido, false si no
}
```

### Manejo de Errores

Las funciones de obtención de datos incluyen manejo de errores:

```typescript
try {
  const datos = obtenerDatos();
  return datos.filter(validarElemento);
} catch (error) {
  console.error('Error al cargar datos:', error);
  return [];
}
```

## 📊 Tipos de Estado de Proyecto

```typescript
type EstadoProyecto = 'planificacion' | 'en-progreso' | 'completado' | 'pausado';
```

## 🎯 Patrones de Uso Comunes

### Obtener y Filtrar Datos

```typescript
// Obtener todos los elementos
const servicios = getServicios();
const equipo = getEquipo();
const proyectos = getProyectos();

// Filtrar por criterios
const serviciosConPrecio = servicios.filter(s => s.servicio.precio);
const equipoConEspecialidades = equipo.filter(m => m.miembro.especialidades?.length);
const proyectosCompletados = getProyectosPorEstado('completado');
```

### Validación Antes de Uso

```typescript
// Validar datos antes de procesarlos
const datosValidos = datos.filter(validarServicio);
if (datosValidos.length !== datos.length) {
  console.warn('Algunos elementos fueron filtrados por estructura inválida');
}
```

### Generación de Rutas Dinámicas

```typescript
// En páginas [slug].astro
export async function getStaticPaths() {
  const elementos = getElementos();
  
  return elementos.map((elemento) => ({
    params: { slug: elemento.id },
    props: { elemento },
  }));
}
```

---

**Nota**: Todas las funciones incluyen manejo de errores y validación de datos para garantizar la robustez del sistema.
