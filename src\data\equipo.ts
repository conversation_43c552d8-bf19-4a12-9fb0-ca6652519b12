/**
 * Tipos y utilidades para el sistema de equipo
 * 
 * Este módulo proporciona:
 * - Tipos TypeScript para la estructura de miembros del equipo
 * - Función para cargar miembros desde JSON
 * - Validación de estructura de datos
 */

// Tipo para la información detallada del miembro del equipo
export interface MiembroDetalle {
  nombre: string;
  cargo: string;
  descripcion: string;
  experiencia: string;
  especialidades?: string[];
  formacion?: string[];
  certificaciones?: string[];
  idiomas?: string[];
  horarios?: string;
  telefono?: string;
  email?: string;
  linkedin?: string;
  imagenes?: string[];
}

// Tipo para el miembro completo (tarjeta + página detalle)
export interface Miembro {
  id: string;
  image: string;
  name: string;
  position: string;
  description: string;
  alt: string;
  miembro: MiembroDetalle;
}

// Tipo para la estructura completa del archivo JSON
export interface EquipoData {
  _comment?: string;
  _format?: any;
  equipo: Miembro[];
}

// Importar los datos desde el archivo JSON
import equipoData from './equipo.json';

/**
 * Obtiene todos los miembros del equipo desde el archivo JSON
 * @returns Array de miembros del equipo
 */
export function getEquipo(): Miembro[] {
  const data = equipoData as EquipoData;
  return data.equipo || [];
}

/**
 * Obtiene un miembro específico por su ID
 * @param id - ID del miembro a buscar
 * @returns Miembro encontrado o undefined
 */
export function getMiembroPorId(id: string): Miembro | undefined {
  const equipo = getEquipo();
  return equipo.find(miembro => miembro.id === id);
}

/**
 * Valida la estructura de un miembro del equipo
 * @param miembro - Miembro a validar
 * @returns true si la estructura es válida
 */
export function validarMiembro(miembro: any): miembro is Miembro {
  return (
    typeof miembro === 'object' &&
    typeof miembro.id === 'string' &&
    typeof miembro.image === 'string' &&
    typeof miembro.name === 'string' &&
    typeof miembro.position === 'string' &&
    typeof miembro.description === 'string' &&
    typeof miembro.alt === 'string' &&
    typeof miembro.miembro === 'object' &&
    typeof miembro.miembro.nombre === 'string' &&
    typeof miembro.miembro.cargo === 'string' &&
    typeof miembro.miembro.descripcion === 'string' &&
    typeof miembro.miembro.experiencia === 'string'
  );
}

/**
 * Obtiene todos los IDs de miembros para generación de rutas estáticas
 * @returns Array de IDs de miembros
 */
export function getEquipoIds(): string[] {
  const equipo = getEquipo();
  return equipo.map(miembro => miembro.id);
}
