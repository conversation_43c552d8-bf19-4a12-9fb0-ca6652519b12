---
/**
 * TopOffsetExample - Ejemplo de uso del topOffset en ImageGallery
 */
import ImageGallery from "./ImageGallery.astro";

// Imágenes de ejemplo
const imagenesEjemplo = [
	"/hero_podial_1.png",
	"/logo_podial_1.png",
	"/logo_podial_texto_1.png",
];
---

<!-- Header fixed simulado para demostrar el problema -->
<div class="fixed top-0 left-0 right-0 bg-blue-600 text-white p-4 z-50 shadow-lg">
	<h1 class="text-xl font-bold">Header Fixed (z-50) - Altura: 72px</h1>
</div>

<div class="pt-20 p-8 space-y-12">
	<h2 class="text-3xl font-bold mb-6">Ejemplos de topOffset para Headers Fixed</h2>
	
	<!-- Problema: Sin topOffset -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold text-red-600">❌ Problema: Sin topOffset</h3>
		<p class="text-gray-600">El botón de cerrar queda tapado por el header fixed</p>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Sin topOffset" 
		/>
	</div>

	<!-- Solución: Con topOffset -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold text-green-600">✅ Solución: Con topOffset="80px"</h3>
		<p class="text-gray-600">El botón de cerrar se posiciona correctamente debajo del header</p>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Con topOffset" 
			topOffset="80px"
		/>
	</div>

	<!-- Ejemplo con diferentes offsets -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">🎯 Diferentes valores de topOffset</h3>
		
		<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
			<div>
				<h4 class="font-semibold mb-2">topOffset="120px"</h4>
				<p class="text-sm text-gray-600 mb-4">Para headers más altos o con elementos adicionales</p>
				<ImageGallery 
					imagenes={imagenesEjemplo} 
					titulo="Offset 120px" 
					topOffset="120px"
				/>
			</div>
			
			<div>
				<h4 class="font-semibold mb-2">topOffset="40px"</h4>
				<p class="text-sm text-gray-600 mb-4">Para headers más pequeños</p>
				<ImageGallery 
					imagenes={imagenesEjemplo} 
					titulo="Offset 40px" 
					topOffset="40px"
				/>
			</div>
		</div>
	</div>

	<!-- Combinado con personalización -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">🎨 topOffset + Personalización</h3>
		<p class="text-gray-600">Combina topOffset con otras personalizaciones</p>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Personalizado con Offset" 
			topOffset="80px"
			closeButtonClass="!bg-green-600 hover:!bg-green-700 !w-16 !h-16"
			counterClass="!bg-blue-600 !px-4 !py-2"
			overlayClass="!bg-purple-900 !bg-opacity-95"
		/>
	</div>

	<!-- Documentación -->
	<div class="bg-blue-50 p-6 rounded-lg">
		<h3 class="text-xl font-semibold mb-4">📚 Documentación del topOffset</h3>
		
		<div class="space-y-4">
			<div>
				<h4 class="font-semibold mb-2">¿Qué hace el topOffset?</h4>
				<p class="text-gray-700">
					El parámetro <code class="bg-gray-200 px-2 py-1 rounded">topOffset</code> ajusta la posición 
					del botón de cerrar y el contador para evitar que queden tapados por headers fixed o 
					elementos con z-index alto.
				</p>
			</div>
			
			<div>
				<h4 class="font-semibold mb-2">Valores recomendados:</h4>
				<ul class="space-y-1 text-gray-700">
					<li>• <strong>"80px"</strong> - Para headers estándar (~72px de altura)</li>
					<li>• <strong>"120px"</strong> - Para headers con navegación o elementos adicionales</li>
					<li>• <strong>"60px"</strong> - Para headers más compactos</li>
					<li>• <strong>"0px"</strong> - Sin offset (valor por defecto)</li>
				</ul>
			</div>
			
			<div>
				<h4 class="font-semibold mb-2">Cómo calcular el offset:</h4>
				<ol class="space-y-1 text-gray-700">
					<li>1. Inspecciona la altura de tu header fixed</li>
					<li>2. Añade ~8-16px de margen adicional</li>
					<li>3. Usa ese valor como topOffset</li>
				</ol>
			</div>
			
			<div class="bg-white p-4 rounded border-l-4 border-blue-500">
				<h4 class="font-semibold mb-2">💡 Ejemplo de uso en ModalServicio:</h4>
				<pre class="text-sm bg-gray-100 p-2 rounded overflow-x-auto"><code>&lt;ImageGallery 
	imagenes={servicio.imagenes || []} 
	titulo={servicio.titulo}
	topOffset="80px"
/&gt;</code></pre>
			</div>
		</div>
	</div>
</div>
