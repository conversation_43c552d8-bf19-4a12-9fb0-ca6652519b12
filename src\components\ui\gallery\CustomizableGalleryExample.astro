---
/**
 * CustomizableGalleryExample - Ejemplos de personalización de ImageGallery
 */
import ImageGallery from "./ImageGallery.astro";

// Imágenes de ejemplo
const imagenesEjemplo = [
	"/hero_podial_1.png",
	"/logo_podial_1.png",
	"/logo_podial_texto_1.png",
];
---

<div class="p-8 space-y-12">
	<h2 class="text-3xl font-bold mb-6">Ejemplos de ImageGallery Personalizable</h2>
	
	<!-- <PERSON><PERSON><PERSON> por defecto -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">1. Galería por Defecto</h3>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Galería Normal" 
		/>
	</div>

	<!-- <PERSON>r<PERSON> con overlay personalizado -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">2. Overlay Personalizado (Azul)</h3>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Overlay Azul" 
			overlayClass="!bg-blue-900 !bg-opacity-95"
		/>
	</div>

	<!-- Galería con imágenes personalizadas -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">3. Imágenes con Bordes y Sombras</h3>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Imágenes Personalizadas" 
			imageClass="!rounded-xl border-4 border-primary-200 shadow-xl hover:shadow-2xl !h-32"
		/>
	</div>

	<!-- Galería con botón de cerrar personalizado -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">4. Botón de Cerrar Verde</h3>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Botón Verde" 
			closeButtonClass="!bg-green-600 hover:!bg-green-700 !w-16 !h-16"
		/>
	</div>

	<!-- Galería con botones de navegación personalizados -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">5. Botones de Navegación Cuadrados</h3>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Navegación Cuadrada" 
			navButtonClass="!rounded-lg !bg-purple-600 hover:!bg-purple-700 !w-16 !h-16"
		/>
	</div>

	<!-- Galería con imagen del lightbox personalizada -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">6. Imagen del Lightbox con Borde</h3>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Imagen con Borde" 
			lightboxImageClass="border-8 border-white rounded-lg shadow-2xl"
		/>
	</div>

	<!-- Galería con contador personalizado -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">7. Contador Personalizado</h3>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Contador Personalizado" 
			counterClass="!bg-gradient-to-r !from-purple-600 !to-pink-600 !px-6 !py-3 !text-lg font-bold"
		/>
	</div>

	<!-- Galería completamente personalizada -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">8. Galería Completamente Personalizada</h3>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Tema Oscuro Completo" 
			class="bg-gray-900 p-6 rounded-xl"
			overlayClass="!bg-gray-900 !bg-opacity-98"
			imageClass="!rounded-2xl border-2 border-gray-600 hover:border-yellow-400 !h-40 grayscale hover:grayscale-0"
			closeButtonClass="!bg-red-600 hover:!bg-red-700 !w-14 !h-14 border-2 border-white"
			navButtonClass="!bg-yellow-500 hover:!bg-yellow-600 !text-black !w-14 !h-14 border-2 border-white"
			lightboxImageClass="border-4 border-yellow-400 rounded-xl shadow-2xl"
			counterClass="!bg-yellow-500 !text-black !px-4 !py-2 !text-lg font-bold border-2 border-white"
		/>
	</div>

	<!-- Documentación de clases -->
	<div class="bg-gray-50 p-6 rounded-lg">
		<h3 class="text-xl font-semibold mb-4">📚 Clases Personalizables Disponibles</h3>
		
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			<div>
				<h4 class="font-semibold mb-2">Props de Clases:</h4>
				<ul class="space-y-1 text-sm">
					<li><code class="bg-gray-200 px-2 py-1 rounded">class</code> - Contenedor principal</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">overlayClass</code> - Overlay del lightbox</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">imageClass</code> - Imágenes de la galería</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">lightboxImageClass</code> - Imagen del lightbox</li>
				</ul>
			</div>
			
			<div>
				<h4 class="font-semibold mb-2">Controles del Lightbox:</h4>
				<ul class="space-y-1 text-sm">
					<li><code class="bg-gray-200 px-2 py-1 rounded">closeButtonClass</code> - Botón de cerrar</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">navButtonClass</code> - Botones de navegación</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">counterClass</code> - Contador de imágenes</li>
				</ul>
			</div>
		</div>

		<div class="mt-4 p-4 bg-blue-50 rounded border-l-4 border-blue-500">
			<h4 class="font-semibold mb-2">💡 Consejos de Uso:</h4>
			<ul class="text-sm space-y-1">
				<li>• Usa <code>!</code> para forzar clases (ej: <code>!bg-red-500</code>)</li>
				<li>• Combina múltiples clases separadas por espacios</li>
				<li>• Las clases se concatenan con las existentes</li>
				<li>• Puedes usar cualquier clase de Tailwind CSS</li>
			</ul>
		</div>
	</div>
</div>
