# Guía de Uso del Sistema de Páginas Dinámicas

## 🎯 Introducción

El sistema de páginas dinámicas te permite crear automáticamente páginas individuales para servicios, miembros del equipo y proyectos, basándose en datos JSON. Cada elemento puede mostrarse tanto en modales como en páginas completas.

## 🚀 Inicio Rápido

### URLs Disponibles

Una vez implementado el sistema, tendrás acceso a estas URLs:

**Páginas de Índice:**
- `/servicios` - Lista completa de servicios
- `/equipo` - Lista completa del equipo
- `/proyectos` - Lista completa de proyectos

**Páginas Individuales:**
- `/servicios/quiropodologia` - Página del servicio de quiropodología
- `/servicios/biomecanica` - Página del servicio de biomecánica
- `/equipo/diego-mateos` - Per<PERSON><PERSON> de <PERSON>
- `/proyectos/clinica-madrid` - Proyecto de la clínica de Madrid

## 📝 Gestión de Contenido

### Agregar un Nuevo Servicio

1. **Edita el archivo** `src/data/servicios.json`
2. **Agrega un nuevo objeto** al array `servicios`:

```json
{
  "id": "podologia-deportiva",
  "image": "https://ejemplo.com/imagen.jpg",
  "title": "Podología Deportiva",
  "description": "Tratamiento especializado para atletas",
  "alt": "Podología deportiva",
  "servicio": {
    "titulo": "Podología Deportiva Especializada",
    "descripcion": "Tratamiento integral para deportistas...",
    "icono": "sports",
    "precio": "Desde 60€",
    "duracion": "75 minutos",
    "beneficios": [
      "Prevención de lesiones",
      "Mejora del rendimiento",
      "Análisis biomecánico"
    ],
    "proceso": [
      "Evaluación deportiva",
      "Análisis de la pisada",
      "Tratamiento personalizado"
    ],
    "imagenes": [
      "/images/podologia-deportiva-1.jpg"
    ]
  }
}
```

3. **Guarda el archivo** - La página se generará automáticamente en `/servicios/podologia-deportiva`

### Agregar un Nuevo Miembro del Equipo

1. **Edita el archivo** `src/data/equipo.json`
2. **Agrega un nuevo objeto** al array `equipo`:

```json
{
  "id": "maria-gonzalez",
  "image": "/images/equipo/maria.jpg",
  "name": "María González",
  "position": "Podóloga Especialista",
  "description": "Especialista en pie diabético con 15 años de experiencia",
  "alt": "María González - Podóloga",
  "miembro": {
    "nombre": "María González",
    "cargo": "Podóloga Especialista en Pie Diabético",
    "descripcion": "Especialista con amplia experiencia...",
    "experiencia": "15+ años",
    "especialidades": [
      "Pie diabético",
      "Úlceras plantares",
      "Cuidados preventivos"
    ],
    "formacion": [
      "Diplomada en Podología",
      "Máster en Pie Diabético"
    ],
    "telefono": "+34 987 654 321",
    "email": "<EMAIL>"
  }
}
```

### Agregar un Nuevo Proyecto

1. **Edita el archivo** `src/data/proyectos.json`
2. **Agrega un nuevo objeto** al array:

```json
{
  "id": "clinica-sevilla",
  "title": "Clínica Podológica Sevilla",
  "description": "Modernización integral de clínica en Sevilla",
  "image": "/images/proyectos/sevilla.jpg",
  "alt": "Clínica Sevilla",
  "proyecto": {
    "titulo": "Modernización Clínica Sevilla",
    "descripcion": "Proyecto de renovación completa...",
    "cliente": "Podología Sevilla S.L.",
    "fecha": "Diciembre 2024",
    "estado": "completado",
    "presupuesto": "€95,000",
    "tecnologias": ["Equipos láser", "Software gestión"],
    "resultados": [
      "Aumento 50% eficiencia",
      "Mejora satisfacción pacientes"
    ]
  }
}
```

## 🎨 Personalización de Componentes

### Configurar Navegación Dual

Puedes controlar si mostrar modales, páginas, o ambos:

```astro
<!-- Solo páginas (sin modales) -->
<ServiceCard
    enableModal={false}
    enablePageNavigation={true}
    {...otherProps}
/>

<!-- Solo modales (sin páginas) -->
<ServiceCard
    enableModal={true}
    enablePageNavigation={false}
    {...otherProps}
/>

<!-- Ambos (por defecto) -->
<ServiceCard
    enableModal={true}
    enablePageNavigation={true}
    {...otherProps}
/>
```

### Personalizar el Layout de Detalle

El layout `DetailLayout.astro` acepta estas props:

```astro
<DetailLayout 
    title="Título de la página"
    description="Descripción para SEO"
    image="/imagen-hero.jpg"
    breadcrumbs={[
        { name: 'Inicio', href: '/' },
        { name: 'Servicios', href: '/servicios' },
        { name: 'Quiropodología', href: '/servicios/quiropodologia' }
    ]}
>
    <!-- Contenido específico aquí -->
</DetailLayout>
```

## 🔧 Funciones Útiles

### Obtener Datos

```typescript
import { getServicios, getServicioPorId } from '../data/servicios';
import { getEquipo, getMiembroPorId } from '../data/equipo';
import { getProyectos, getProyectoPorId } from '../data/proyectos';

// Obtener todos los servicios
const servicios = getServicios();

// Obtener un servicio específico
const servicio = getServicioPorId('quiropodologia');

// Obtener estadísticas de proyectos
const stats = getEstadisticasProyectos();
```

### Validar Datos

```typescript
import { validarServicio } from '../data/servicios';

const esValido = validarServicio(miServicio);
if (!esValido) {
    console.error('Estructura de servicio inválida');
}
```

## 📱 Características de las Páginas

### Páginas de Servicios
- **Información completa**: Descripción, precio, duración
- **Beneficios**: Lista de ventajas del servicio
- **Proceso**: Pasos del tratamiento
- **Galería**: Imágenes adicionales si están disponibles
- **Call-to-action**: Botones para contactar

### Páginas de Equipo
- **Perfil profesional**: Experiencia y cargo
- **Especialidades**: Áreas de expertise
- **Formación**: Educación y certificaciones
- **Contacto**: Información de contacto directo
- **Horarios**: Disponibilidad si está configurada

### Páginas de Proyectos
- **Detalles del proyecto**: Cliente, fecha, presupuesto
- **Estado**: Indicador visual del progreso
- **Tecnologías**: Herramientas utilizadas
- **Resultados**: Métricas de éxito
- **Equipo**: Profesionales involucrados

## 🎯 Mejores Prácticas

### Contenido
- **Imágenes**: Usa URLs absolutas o rutas relativas válidas
- **Descripciones**: Mantén descripciones concisas pero informativas
- **IDs únicos**: Asegúrate de que cada ID sea único y descriptivo

### SEO
- **Títulos**: Usa títulos descriptivos y únicos
- **Descripciones**: Incluye palabras clave relevantes
- **Imágenes**: Proporciona texto alternativo descriptivo

### Performance
- **Imágenes**: Optimiza el tamaño de las imágenes
- **Contenido**: Evita descripciones excesivamente largas
- **Build**: Regenera el sitio después de cambios en JSON

## 🐛 Solución de Problemas

### Página no se genera
- ✅ Verifica que el ID sea único
- ✅ Revisa la sintaxis JSON
- ✅ Asegúrate de que todos los campos requeridos estén presentes

### Imagen no se muestra
- ✅ Verifica la URL de la imagen
- ✅ Asegúrate de que la imagen sea accesible
- ✅ Revisa el texto alternativo

### Error de build
- ✅ Valida la estructura JSON
- ✅ Verifica que no haya IDs duplicados
- ✅ Revisa los tipos TypeScript

---

¿Necesitas ayuda con algún aspecto específico? Consulta la [documentación técnica](./arquitectura.md) o los [ejemplos prácticos](./ejemplos.md).
