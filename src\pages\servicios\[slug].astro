---
import DetailLayout from '../../layouts/DetailLayout.astro';
import { getServicios, type Servicio } from '../../data/servicios';

export async function getStaticPaths() {
	const servicios = getServicios();
	
	return servicios.map((servicio) => ({
		params: { slug: servicio.id },
		props: { servicio },
	}));
}

interface Props {
	servicio: Servicio;
}

const { servicio } = Astro.props;

const breadcrumbs = [
	{ name: 'Servic<PERSON>', href: '/#servicios' },
	{ name: servicio.title, href: `/servicios/${servicio.id}` }
];
---

<DetailLayout 
	title={servicio.servicio.titulo}
	description={servicio.servicio.descripcion}
	image={servicio.image}
	breadcrumbs={breadcrumbs}
>
	<!-- Información Principal -->
	<div class="bg-white rounded-xl shadow-lg p-8 mb-8">
		<div class="flex items-center mb-6">
			<div class="bg-primary-100 rounded-full p-4 mr-4">
				<span class="material-symbols-outlined text-primary-600 text-3xl">
					{servicio.servicio.icono}
				</span>
			</div>
			<div>
				{servicio.servicio.precio && (
					<p class="text-2xl font-bold text-primary-600 mb-1">
						{servicio.servicio.precio}
					</p>
				)}
				{servicio.servicio.duracion && (
					<p class="text-gray-600">
						<span class="material-symbols-outlined text-sm mr-1">schedule</span>
						{servicio.servicio.duracion}
					</p>
				)}
			</div>
		</div>
		
		<div class="prose prose-lg max-w-none">
			<p class="text-gray-700 leading-relaxed">
				{servicio.servicio.descripcion}
			</p>
		</div>
	</div>

	<!-- Beneficios -->
	{servicio.servicio.beneficios && servicio.servicio.beneficios.length > 0 && (
		<div class="bg-white rounded-xl shadow-lg p-8 mb-8">
			<h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
				<span class="material-symbols-outlined text-primary-600 mr-3">check_circle</span>
				Beneficios
			</h2>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				{servicio.servicio.beneficios.map((beneficio) => (
					<div class="flex items-start">
						<span class="material-symbols-outlined text-green-500 mr-3 mt-1">check</span>
						<p class="text-gray-700">{beneficio}</p>
					</div>
				))}
			</div>
		</div>
	)}

	<!-- Proceso -->
	{servicio.servicio.proceso && servicio.servicio.proceso.length > 0 && (
		<div class="bg-white rounded-xl shadow-lg p-8 mb-8">
			<h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
				<span class="material-symbols-outlined text-primary-600 mr-3">list</span>
				Proceso del Tratamiento
			</h2>
			<div class="space-y-4">
				{servicio.servicio.proceso.map((paso, index) => (
					<div class="flex items-start">
						<div class="bg-primary-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1 text-sm font-bold">
							{index + 1}
						</div>
						<p class="text-gray-700 pt-1">{paso}</p>
					</div>
				))}
			</div>
		</div>
	)}

	<!-- Galería de Imágenes -->
	{servicio.servicio.imagenes && servicio.servicio.imagenes.length > 0 && (
		<div class="bg-white rounded-xl shadow-lg p-8 mb-8">
			<h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
				<span class="material-symbols-outlined text-primary-600 mr-3">photo_library</span>
				Galería
			</h2>
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				{servicio.servicio.imagenes.map((imagen, index) => (
					<div class="rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
						<img 
							src={imagen} 
							alt={`${servicio.title} - Imagen ${index + 1}`}
							class="w-full h-48 object-cover hover:scale-105 transition-transform duration-300"
						/>
					</div>
				))}
			</div>
		</div>
	)}

	<!-- Call to Action -->
	<div class="bg-primary-50 rounded-xl p-8 text-center">
		<h3 class="text-2xl font-bold text-gray-900 mb-4">
			¿Interesado en este servicio?
		</h3>
		<p class="text-gray-600 mb-6">
			Contacta con nosotros para más información o para reservar tu cita
		</p>
		<div class="flex flex-col sm:flex-row gap-4 justify-center">
			<a 
				href="/#contacto" 
				class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
			>
				<span class="material-symbols-outlined mr-2">calendar_today</span>
				Reservar Cita
			</a>
			<a 
				href="tel:+34123456789" 
				class="inline-flex items-center px-6 py-3 bg-white text-primary-600 border border-primary-600 rounded-lg hover:bg-primary-50 transition-colors"
			>
				<span class="material-symbols-outlined mr-2">call</span>
				Llamar Ahora
			</a>
		</div>
	</div>
</DetailLayout>
