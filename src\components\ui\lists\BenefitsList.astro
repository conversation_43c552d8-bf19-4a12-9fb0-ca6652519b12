---
/**
 * BenefitsList - Componente reutilizable para mostrar listas de beneficios
 *
 * Este componente muestra una lista de beneficios con iconos de check.
 * Diseñado para ser completamente reutilizable en otros proyectos.
 *
 * @param beneficios - Array de strings con los beneficios
 * @param titulo - Título de la sección (default: "Beneficios")
 * @param showTitle - Si mostrar o no el título (default: true)
 * @param imagen - URL de imagen opcional para mostrar al lado
 * @param imagenAlt - Alt text para la imagen
 * @param showImage - Si mostrar la imagen cuando está disponible (default: true)
 * @param layout - Layout: 'single' | 'with-image' (default: 'auto')
 * @param class - Clases adicionales para el contenedor
 * @param titleClass - Clases adicionales para el título
 * @param listClass - Clases adicionales para la lista
 * @param itemClass - Clases adicionales para cada item
 * @param iconClass - Clases adicionales para los iconos
 */

export interface Props {
	beneficios: string[];
	titulo?: string;
	showTitle?: boolean;
	imagen?: string;
	imagenAlt?: string;
	showImage?: boolean;
	layout?: 'single' | 'with-image' | 'auto';
	class?: string;
	titleClass?: string;
	listClass?: string;
	itemClass?: string;
	iconClass?: string;
}

const {
	beneficios,
	titulo = "Beneficios",
	showTitle = true,
	imagen,
	imagenAlt = "",
	showImage = true,
	layout = 'auto',
	class: className = "",
	titleClass = "",
	listClass = "",
	itemClass = "",
	iconClass = "",
} = Astro.props;

// Determinar el layout automáticamente
const shouldShowImage = showImage && imagen && layout !== 'single';
const finalLayout = layout === 'auto' ? (shouldShowImage ? 'with-image' : 'single') : layout;
const useGridLayout = finalLayout === 'with-image' && shouldShowImage;
---

{
	beneficios && beneficios.length > 0 && (
		<div class={`${useGridLayout ? 'grid grid-cols-1 md:grid-cols-2 gap-6 items-start' : ''} ${className}`}>
			<!-- Lista de beneficios -->
			<div>
				{showTitle && (
					<h3 class={`font-semibold text-gray-800 mb-3 flex items-center ${titleClass}`}>
						<span class={`material-symbols-outlined text-green-600 mr-2 ${iconClass}`}>
							check_circle
						</span>
						{titulo}
					</h3>
				)}
				<ul class={`space-y-2 ${listClass}`}>
					{beneficios.map((beneficio) => (
						<li class={`flex items-start ${itemClass}`}>
							<span class={`material-symbols-outlined text-green-500 text-xl mr-2 mt-0.5 ${iconClass}`}>
								check
							</span>
							<span class="text-gray-700">{beneficio}</span>
						</li>
					))}
				</ul>
			</div>

			<!-- Imagen si existe y está habilitada -->
			{shouldShowImage && (
				<div class="overflow-hidden rounded-xl shadow-lg">
					<img
						src={imagen}
						alt={imagenAlt || `${titulo} imagen`}
						class="w-full h-full object-cover transition-transform hover:scale-105 duration-500"
					/>
				</div>
			)}
		</div>
	)
}
