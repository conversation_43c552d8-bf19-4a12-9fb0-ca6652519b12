---
/**
 * BenefitsListExample - Ejemplos de uso del componente BenefitsList
 */
import BenefitsList from "./BenefitsList.astro";

// Datos de ejemplo
const beneficiosEjemplo = [
	"Mejora la salud y bienestar general",
	"Reduce el dolor y las molestias",
	"Previene problemas futuros",
	"Tratamiento personalizado y profesional",
	"Resultados visibles desde la primera sesión"
];

const beneficiosCortos = [
	"Rápido y efectivo",
	"Sin dolor",
	"Resultados duraderos"
];

const beneficiosLargos = [
	"Tratamiento integral que aborda tanto los síntomas como las causas subyacentes del problema",
	"Uso de tecnología avanzada y técnicas modernas para garantizar los mejores resultados",
	"Seguimiento personalizado con plan de cuidados adaptado a cada paciente",
	"Equipo de profesionales altamente cualificados con años de experiencia",
	"Ambiente cómodo y relajante que favorece la recuperación",
	"Horarios flexibles que se adaptan a tu rutina diaria"
];
---

<div class="p-8 space-y-12">
	<h2 class="text-3xl font-bold mb-6">Ejemplos de BenefitsList</h2>
	
	<!-- Lista básica -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">1. Lista Básica</h3>
		<BenefitsList beneficios={beneficiosEjemplo} />
	</div>

	<!-- Lista con imagen -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">2. Lista con Imagen</h3>
		<BenefitsList 
			beneficios={beneficiosEjemplo}
			imagen="/hero_podial_1.png"
			imagenAlt="Tratamiento podológico"
		/>
	</div>

	<!-- Lista sin título -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">3. Lista sin Título</h3>
		<BenefitsList 
			beneficios={beneficiosCortos}
			showTitle={false}
		/>
	</div>

	<!-- Lista con título personalizado -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">4. Título Personalizado</h3>
		<BenefitsList 
			beneficios={beneficiosEjemplo}
			titulo="Ventajas del Tratamiento"
		/>
	</div>

	<!-- Lista forzada a una columna -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">5. Layout de Una Columna (con imagen disponible)</h3>
		<BenefitsList 
			beneficios={beneficiosEjemplo}
			imagen="/hero_podial_1.png"
			layout="single"
		/>
	</div>

	<!-- Lista con estilos personalizados -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">6. Estilos Personalizados</h3>
		<BenefitsList 
			beneficios={beneficiosCortos}
			titulo="Beneficios Destacados"
			class="bg-blue-50 p-6 rounded-xl"
			titleClass="text-blue-800"
			iconClass="!text-blue-600"
			itemClass="bg-white p-2 rounded-lg shadow-sm"
		/>
	</div>

	<!-- Lista con tema oscuro -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">7. Tema Oscuro</h3>
		<BenefitsList 
			beneficios={beneficiosEjemplo}
			titulo="Beneficios Premium"
			imagen="/hero_podial_1.png"
			class="bg-gray-900 p-6 rounded-xl"
			titleClass="!text-white"
			listClass="space-y-3"
			itemClass="bg-gray-800 p-3 rounded-lg"
			iconClass="!text-yellow-400"
		/>
	</div>

	<!-- Lista con muchos beneficios -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">8. Lista Extensa</h3>
		<BenefitsList 
			beneficios={beneficiosLargos}
			titulo="Beneficios Completos"
			imagen="/hero_podial_1.png"
		/>
	</div>

	<!-- Lista con imagen pero sin mostrarla -->
	<div class="space-y-4">
		<h3 class="text-xl font-semibold">9. Imagen Deshabilitada</h3>
		<BenefitsList 
			beneficios={beneficiosEjemplo}
			imagen="/hero_podial_1.png"
			showImage={false}
		/>
	</div>

	<!-- Documentación -->
	<div class="bg-green-50 p-6 rounded-lg">
		<h3 class="text-xl font-semibold mb-4">📚 Documentación de BenefitsList</h3>
		
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			<div>
				<h4 class="font-semibold mb-2">Props Principales:</h4>
				<ul class="space-y-1 text-sm">
					<li><code class="bg-gray-200 px-2 py-1 rounded">beneficios</code> - Array de strings (requerido)</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">titulo</code> - Título de la sección</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">showTitle</code> - Mostrar título (default: true)</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">imagen</code> - URL de imagen opcional</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">showImage</code> - Mostrar imagen (default: true)</li>
				</ul>
			</div>
			
			<div>
				<h4 class="font-semibold mb-2">Props de Personalización:</h4>
				<ul class="space-y-1 text-sm">
					<li><code class="bg-gray-200 px-2 py-1 rounded">class</code> - Contenedor principal</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">titleClass</code> - Título</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">listClass</code> - Lista</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">itemClass</code> - Items individuales</li>
					<li><code class="bg-gray-200 px-2 py-1 rounded">iconClass</code> - Iconos</li>
				</ul>
			</div>
		</div>

		<div class="mt-4 p-4 bg-white rounded border-l-4 border-green-500">
			<h4 class="font-semibold mb-2">💡 Layouts Disponibles:</h4>
			<ul class="text-sm space-y-1">
				<li>• <strong>'auto'</strong> - Detecta automáticamente si usar imagen (default)</li>
				<li>• <strong>'single'</strong> - Una columna, sin imagen</li>
				<li>• <strong>'with-image'</strong> - Dos columnas con imagen</li>
			</ul>
		</div>

		<div class="mt-4 p-4 bg-white rounded border-l-4 border-green-500">
			<h4 class="font-semibold mb-2">🎯 Uso en ModalServicio:</h4>
			<pre class="text-sm bg-gray-100 p-2 rounded overflow-x-auto"><code>&lt;BenefitsList 
	beneficios={servicio.beneficios || []}
	imagen={servicio.imagenes?.[0]}
	imagenAlt={`${servicio.titulo} beneficios`}
/&gt;</code></pre>
		</div>
	</div>
</div>
