/**
 * Utilidades para el sistema de modales extensible
 * 
 * Este archivo contiene funciones utilitarias para generar IDs de modales
 * de forma consistente y manejar diferentes tipos de modales.
 */

/**
 * Tipos de modales soportados por el sistema
 */
export type ModalType = 'servicio' | 'proyecto' | 'feature' | 'contacto' | 'custom';

/**
 * Configuración para diferentes tipos de modales
 */
export interface ModalConfig {
  type: ModalType;
  prefix?: string;
  defaultSize?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnBackdrop?: boolean;
}

/**
 * Configuraciones predefinidas para cada tipo de modal
 */
export const MODAL_CONFIGS: Record<ModalType, ModalConfig> = {
  servicio: {
    type: 'servicio',
    defaultSize: 'lg',
    closeOnBackdrop: true,
  },
  proyecto: {
    type: 'proyecto',
    defaultSize: 'xl',
    closeOnBackdrop: true,
  },
  feature: {
    type: 'feature',
    defaultSize: 'md',
    closeOnBackdrop: true,
  },
  contacto: {
    type: 'contacto',
    defaultSize: 'md',
    closeOnBackdrop: true,
  },
  custom: {
    type: 'custom',
    defaultSize: 'md',
    closeOnBackdrop: true,
  },
};

/**
 * Genera un ID de modal siguiendo el patrón modal-{tipo}-{id}
 * 
 * @param type - Tipo de modal
 * @param id - ID único del elemento
 * @returns ID del modal en formato modal-{tipo}-{id}
 * 
 * @example
 * ```typescript
 * generateModalId('servicio', 'podologia-general') // 'modal-servicio-podologia-general'
 * generateModalId('proyecto', 'clinica-madrid') // 'modal-proyecto-clinica-madrid'
 * ```
 */
export function generateModalId(type: ModalType, id: string): string {
  return `modal-${type}-${id}`;
}

/**
 * Extrae el tipo y el ID de un ID de modal
 * 
 * @param modalId - ID del modal en formato modal-{tipo}-{id}
 * @returns Objeto con el tipo y el ID extraídos, o null si el formato no es válido
 * 
 * @example
 * ```typescript
 * parseModalId('modal-servicio-podologia-general') // { type: 'servicio', id: 'podologia-general' }
 * parseModalId('modal-proyecto-clinica-madrid') // { type: 'proyecto', id: 'clinica-madrid' }
 * parseModalId('invalid-id') // null
 * ```
 */
export function parseModalId(modalId: string): { type: ModalType; id: string } | null {
  const match = modalId.match(/^modal-([^-]+)-(.+)$/);
  if (!match) return null;
  
  const [, type, id] = match;
  
  // Verificar que el tipo sea válido
  if (!Object.keys(MODAL_CONFIGS).includes(type)) {
    return null;
  }
  
  return {
    type: type as ModalType,
    id,
  };
}

/**
 * Obtiene la configuración para un tipo de modal específico
 * 
 * @param type - Tipo de modal
 * @returns Configuración del modal
 */
export function getModalConfig(type: ModalType): ModalConfig {
  return MODAL_CONFIGS[type] || MODAL_CONFIGS.custom;
}

/**
 * Verifica si un ID de modal es válido
 * 
 * @param modalId - ID del modal a verificar
 * @returns true si el ID es válido, false en caso contrario
 */
export function isValidModalId(modalId: string): boolean {
  return parseModalId(modalId) !== null;
}

/**
 * Genera un selector CSS para un tipo específico de modal
 * 
 * @param type - Tipo de modal
 * @returns Selector CSS que coincide con todos los modales de ese tipo
 * 
 * @example
 * ```typescript
 * getModalTypeSelector('servicio') // '[id^="modal-servicio-"]'
 * ```
 */
export function getModalTypeSelector(type: ModalType): string {
  return `[id^="modal-${type}-"]`;
}

/**
 * Obtiene todos los tipos de modales disponibles
 * 
 * @returns Array con todos los tipos de modales soportados
 */
export function getAvailableModalTypes(): ModalType[] {
  return Object.keys(MODAL_CONFIGS) as ModalType[];
}

/**
 * Crea un objeto de datos para pasar al modal manager
 * 
 * @param type - Tipo de modal
 * @param id - ID del elemento
 * @param data - Datos adicionales para el modal
 * @returns Objeto con la información del modal
 */
export function createModalData<T = any>(
  type: ModalType,
  id: string,
  data?: T
): {
  modalId: string;
  type: ModalType;
  elementId: string;
  config: ModalConfig;
  data?: T;
} {
  return {
    modalId: generateModalId(type, id),
    type,
    elementId: id,
    config: getModalConfig(type),
    data,
  };
}
