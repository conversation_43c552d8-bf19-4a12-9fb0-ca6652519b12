# Guía de Implementación - Sistema de Páginas Dinámicas

## 🎯 Para Desarrolladores

Esta guía explica cómo implementar y extender el sistema de páginas dinámicas desde una perspectiva técnica.

## 🏗️ Implementación Paso a Paso

### 1. Crear Estructura de Datos

#### Archivo JSON

```json
// src/data/nuevo-tipo.json
{
	"_comment": "Configuración para nuevo tipo de contenido",
	"_format": {
		"description": "Estructura requerida para cada elemento",
		"required_fields": {
			"id": "string - Identificador único",
			"title": "string - Título para la tarjeta",
			"description": "string - Descripción breve",
			"image": "string - URL de la imagen",
			"alt": "string - Texto alternativo"
		}
	},
	"elementos": [
		{
			"id": "ejemplo-1",
			"title": "Elemento de Ejemplo",
			"description": "Descripción del elemento",
			"image": "/images/ejemplo.jpg",
			"alt": "Imagen de ejemplo",
			"detalle": {
				"titulo": "Título completo",
				"descripcion": "Descripción detallada",
				"propiedades_especificas": "Datos específicos del tipo"
			}
		}
	]
}
```

#### Utilidades TypeScript

```typescript
// src/data/nuevo-tipo.ts
export interface DetalleElemento {
	titulo: string;
	descripcion: string;
	propiedades_especificas?: string;
	// Agregar campos específicos según necesidades
}

export interface Elemento {
	id: string;
	title: string;
	description: string;
	image: string;
	alt: string;
	detalle: DetalleElemento;
}

export interface ElementosData {
	_comment?: string;
	_format?: any;
	elementos: Elemento[];
}

import elementosData from "./nuevo-tipo.json";

export function getElementos(): Elemento[] {
	const data = elementosData as ElementosData;
	return data.elementos || [];
}

export function getElementoPorId(id: string): Elemento | undefined {
	const elementos = getElementos();
	return elementos.find((elemento) => elemento.id === id);
}

export function validarElemento(elemento: any): elemento is Elemento {
	return (
		typeof elemento === "object" &&
		typeof elemento.id === "string" &&
		typeof elemento.title === "string" &&
		typeof elemento.description === "string" &&
		typeof elemento.image === "string" &&
		typeof elemento.alt === "string" &&
		typeof elemento.detalle === "object" &&
		typeof elemento.detalle.titulo === "string" &&
		typeof elemento.detalle.descripcion === "string"
	);
}
```

### 2. Crear Componente de Tarjeta

```astro
---
// src/components/ElementoCard.astro
export interface Props {
  title: string;
  description: string;
  image: string;
  alt: string;
  elementoId: string;
  // Props para navegación dual
  enableModal?: boolean;
  enablePageNavigation?: boolean;
  // Props específicas del tipo
  propiedadEspecifica?: string;
}

const {
  title,
  description,
  image,
  alt,
  elementoId,
  enableModal = false,
  enablePageNavigation = true,
  propiedadEspecifica
} = Astro.props;
---

<div
  class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer flex flex-col"
  {...enableModal
    ? { "data-modal-target": `modal-elemento-${elementoId}` }
    : {}}
>
  <!-- Imagen -->
  <div class="h-48 overflow-hidden">
    <img
      src={image}
      alt={alt}
      class="w-full h-full object-cover transition-transform hover:scale-110 duration-500"
    />
  </div>

  <!-- Contenido -->
  <div class="p-6 flex flex-col flex-1 gap-4">
    <div>
      <h3 class="font-bold text-xl mb-2 text-gray-800">{title}</h3>
      <p class="text-gray-600">{description}</p>
      {propiedadEspecifica && (
        <p class="text-sm text-primary-600 mt-2">{propiedadEspecifica}</p>
      )}
    </div>

    <!-- Botones de acción -->
    <div class="mt-auto flex flex-col sm:flex-row gap-3">
      {enableModal && (
        <button
          data-modal-target={`modal-elemento-${elementoId}`}
          class="text-primary-600 hover:text-primary-800 font-medium inline-flex items-center justify-center transition-colors"
        >
          Ver en modal
          <span class="material-symbols-outlined text-sm ml-1">
            open_in_new
          </span>
        </button>
      )}

      {enablePageNavigation && (
        <a
          href={`/elementos/${elementoId}`}
          class="text-primary-600 hover:text-primary-800 font-medium inline-flex items-center justify-center transition-colors"
        >
          Ver detalles
          <span class="material-symbols-outlined text-sm ml-1">
            arrow_forward
          </span>
        </a>
      )}
    </div>
  </div>
</div>
```

### 3. Crear Página Dinámica

```astro
---
// src/pages/elementos/[slug].astro
import DetailLayout from '../../layouts/DetailLayout.astro';
import { getElementos, type Elemento } from '../../data/nuevo-tipo';

export async function getStaticPaths() {
  const elementos = getElementos();

  return elementos.map((elemento) => ({
    params: { slug: elemento.id },
    props: { elemento },
  }));
}

interface Props {
  elemento: Elemento;
}

const { elemento } = Astro.props;

const breadcrumbs = [
  { name: 'Elementos', href: '/elementos' },
  { name: elemento.title, href: `/elementos/${elemento.id}` }
];
---

<DetailLayout
  title={elemento.detalle.titulo}
  description={elemento.detalle.descripcion}
  image={elemento.image}
  breadcrumbs={breadcrumbs}
>
  <!-- Contenido específico del tipo -->
  <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
    <div class="prose prose-lg max-w-none">
      <p class="text-gray-700 leading-relaxed">
        {elemento.detalle.descripcion}
      </p>
    </div>

    <!-- Agregar secciones específicas según el tipo -->
    {elemento.detalle.propiedades_especificas && (
      <div class="mt-6 p-4 bg-primary-50 rounded-lg">
        <h3 class="font-semibold text-gray-800 mb-2">Información Adicional</h3>
        <p class="text-gray-700">{elemento.detalle.propiedades_especificas}</p>
      </div>
    )}
  </div>

  <!-- Call to Action -->
  <div class="bg-primary-50 rounded-xl p-8 text-center">
    <h3 class="text-2xl font-bold text-gray-900 mb-4">
      ¿Interesado en este elemento?
    </h3>
    <p class="text-gray-600 mb-6">
      Contacta con nosotros para más información
    </p>
    <a
      href="/#contacto"
      class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
    >
      <span class="material-symbols-outlined mr-2">contact_mail</span>
      Contactar
    </a>
  </div>
</DetailLayout>
```

### 4. Crear Página de Índice

```astro
---
// src/pages/elementos/index.astro
import Layout from '../../layouts/Layout.astro';
import ElementoCard from '../../components/ElementoCard.astro';
import { getElementos } from '../../data/nuevo-tipo';

const elementos = getElementos();
---

<Layout title="Elementos - Mi Sitio" description="Lista completa de elementos">
  <main class="min-h-screen bg-gray-50">
    <!-- Breadcrumbs -->
    <nav class="bg-white border-b border-gray-200">
      <div class="container mx-auto px-4 py-3">
        <ol class="flex items-center space-x-2 text-sm text-gray-600">
          <li>
            <a href="/" class="hover:text-primary-600 transition-colors">
              <span class="material-symbols-outlined text-base">home</span>
            </a>
          </li>
          <li class="flex items-center">
            <span class="material-symbols-outlined text-gray-400 mx-2">chevron_right</span>
            <span class="text-gray-900 font-medium">Elementos</span>
          </li>
        </ol>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-white py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            Nuestros Elementos
          </h1>
          <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
            Descripción de la sección de elementos
          </p>
        </div>
      </div>
    </section>

    <!-- Grid de Elementos -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {
            elementos.map((elemento) => (
              <ElementoCard
                title={elemento.title}
                description={elemento.description}
                image={elemento.image}
                alt={elemento.alt}
                elementoId={elemento.id}
                enableModal={false}
                enablePageNavigation={true}
                propiedadEspecifica={elemento.detalle.propiedades_especificas}
              />
            ))
          }
        </div>
      </div>
    </section>
  </main>
</Layout>
```

## 🔧 Patrones de Implementación

### 1. Validación de Datos

```typescript
// Implementar validación robusta
export function validarElemento(elemento: any): elemento is Elemento {
	const camposRequeridos = ["id", "title", "description", "image", "alt"];

	for (const campo of camposRequeridos) {
		if (!elemento[campo] || typeof elemento[campo] !== "string") {
			console.error(`Campo requerido faltante o inválido: ${campo}`);
			return false;
		}
	}

	if (!elemento.detalle || typeof elemento.detalle !== "object") {
		console.error("Campo detalle requerido");
		return false;
	}

	return true;
}
```

### 2. Manejo de Errores

```typescript
// Funciones con manejo de errores
export function getElementos(): Elemento[] {
	try {
		const data = elementosData as ElementosData;
		const elementos = data.elementos || [];

		// Validar cada elemento
		const elementosValidos = elementos.filter(validarElemento);

		if (elementosValidos.length !== elementos.length) {
			console.warn(
				"Algunos elementos fueron filtrados por estructura inválida"
			);
		}

		return elementosValidos;
	} catch (error) {
		console.error("Error al cargar elementos:", error);
		return [];
	}
}
```

### 3. Extensibilidad

```typescript
// Interfaces extensibles
export interface DetalleElementoBase {
	titulo: string;
	descripcion: string;
}

export interface DetalleServicio extends DetalleElementoBase {
	precio?: string;
	duracion?: string;
	beneficios?: string[];
}

export interface DetalleMiembro extends DetalleElementoBase {
	experiencia: string;
	especialidades?: string[];
	contacto?: string;
}
```

## 🚀 Optimizaciones

### 1. Performance

```typescript
// Lazy loading de datos
export const getElementosLazy = () =>
	import("./nuevo-tipo.json").then(
		(module) => (module.default as ElementosData).elementos || []
	);

// Memoización
let elementosCache: Elemento[] | null = null;

export function getElementosCached(): Elemento[] {
	if (elementosCache === null) {
		elementosCache = getElementos();
	}
	return elementosCache;
}
```

### 2. SEO

```astro
---
// Meta tags dinámicos
const seoTitle = `${elemento.detalle.titulo} | Mi Sitio`;
const seoDescription = elemento.detalle.descripcion.substring(0, 160);
const seoImage = elemento.image;
---

<DetailLayout
  title={seoTitle}
  description={seoDescription}
  image={seoImage}
>
  <!-- Structured data -->
  <script type="application/ld+json">
    {JSON.stringify({
      "@context": "https://schema.org",
      "@type": "Service", // o el tipo apropiado
      "name": elemento.detalle.titulo,
      "description": elemento.detalle.descripcion,
      "image": elemento.image
    })}
  </script>
</DetailLayout>
```

## 🧪 Testing

```typescript
// Tests unitarios para utilidades
import { describe, it, expect } from "vitest";
import { validarElemento, getElementoPorId } from "../data/nuevo-tipo";

describe("Utilidades de Elementos", () => {
	it("debe validar elemento correcto", () => {
		const elemento = {
			id: "test",
			title: "Test",
			description: "Test desc",
			image: "/test.jpg",
			alt: "Test alt",
			detalle: {
				titulo: "Test titulo",
				descripcion: "Test descripcion",
			},
		};

		expect(validarElemento(elemento)).toBe(true);
	});

	it("debe encontrar elemento por ID", () => {
		const elemento = getElementoPorId("test-id");
		expect(elemento).toBeDefined();
		expect(elemento?.id).toBe("test-id");
	});
});
```

## 📋 Checklist de Implementación

### ✅ Antes de Empezar

-  [ ] Definir estructura de datos específica
-  [ ] Identificar campos requeridos vs opcionales
-  [ ] Planificar URLs y navegación
-  [ ] Considerar casos de uso específicos

### ✅ Durante la Implementación

-  [ ] Crear archivo JSON con datos de ejemplo
-  [ ] Implementar interfaces TypeScript
-  [ ] Crear funciones de utilidad y validación
-  [ ] Desarrollar componente de tarjeta
-  [ ] Implementar página dinámica
-  [ ] Crear página de índice
-  [ ] Agregar tests unitarios

### ✅ Después de la Implementación

-  [ ] Probar generación de páginas estáticas
-  [ ] Validar SEO y meta tags
-  [ ] Verificar responsive design
-  [ ] Documentar uso específico
-  [ ] Actualizar navegación principal

---

**Nota**: Esta guía proporciona un framework completo para implementar nuevos tipos de contenido siguiendo los mismos patrones del sistema existente.
