---
/**
 * ModalProyecto - Componente para mostrar detalles de proyectos
 * 
 * Este componente demuestra cómo extender el sistema de modales
 * para un nuevo tipo de contenido (proyectos).
 * 
 * Uso:
 * ```astro
 * <ModalProyecto 
 *   id="modal-proyecto-clinica-madrid" 
 *   proyecto={proyectoData} 
 * />
 * ```
 */
import ModalBase from "./ModalBase.astro";

export interface Props {
	id: string;
	proyecto: {
		titulo: string;
		descripcion: string;
		cliente?: string;
		fecha?: string;
		ubicacion?: string;
		estado?: 'planificacion' | 'en-progreso' | 'completado' | 'pausado';
		tecnologias?: string[];
		caracteristicas?: string[];
		imagenes?: string[];
		presupuesto?: string;
		duracion?: string;
		equipo?: string[];
		resultados?: string[];
	};
}

const { id, proyecto } = Astro.props;

// Función para obtener el color del estado
const getEstadoColor = (estado: string) => {
	switch (estado) {
		case 'planificacion': return 'bg-yellow-50 text-yellow-700 border-yellow-200';
		case 'en-progreso': return 'bg-blue-50 text-blue-700 border-blue-200';
		case 'completado': return 'bg-green-50 text-green-700 border-green-200';
		case 'pausado': return 'bg-gray-50 text-gray-700 border-gray-200';
		default: return 'bg-gray-50 text-gray-700 border-gray-200';
	}
};

const getEstadoTexto = (estado: string) => {
	switch (estado) {
		case 'planificacion': return 'En Planificación';
		case 'en-progreso': return 'En Progreso';
		case 'completado': return 'Completado';
		case 'pausado': return 'Pausado';
		default: return estado;
	}
};
---

<ModalBase id={id} title={proyecto.titulo} size="xl">
	<div class="space-y-6">
		<!-- Descripción principal -->
		<div class="text-gray-700 leading-relaxed">
			<p>{proyecto.descripcion}</p>
		</div>

		<!-- Información básica del proyecto -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
			{proyecto.cliente && (
				<div class="bg-primary-50 rounded-xl p-4">
					<div class="flex items-center">
						<span class="material-symbols-outlined text-primary-600 mr-2">
							business
						</span>
						<div>
							<p class="text-sm text-gray-600">Cliente</p>
							<p class="font-semibold text-gray-800">{proyecto.cliente}</p>
						</div>
					</div>
				</div>
			)}

			{proyecto.fecha && (
				<div class="bg-blue-50 rounded-xl p-4">
					<div class="flex items-center">
						<span class="material-symbols-outlined text-blue-600 mr-2">
							calendar_today
						</span>
						<div>
							<p class="text-sm text-gray-600">Fecha</p>
							<p class="font-semibold text-gray-800">{proyecto.fecha}</p>
						</div>
					</div>
				</div>
			)}

			{proyecto.ubicacion && (
				<div class="bg-green-50 rounded-xl p-4">
					<div class="flex items-center">
						<span class="material-symbols-outlined text-green-600 mr-2">
							location_on
						</span>
						<div>
							<p class="text-sm text-gray-600">Ubicación</p>
							<p class="font-semibold text-gray-800">{proyecto.ubicacion}</p>
						</div>
					</div>
				</div>
			)}

			{proyecto.presupuesto && (
				<div class="bg-purple-50 rounded-xl p-4">
					<div class="flex items-center">
						<span class="material-symbols-outlined text-purple-600 mr-2">
							payments
						</span>
						<div>
							<p class="text-sm text-gray-600">Presupuesto</p>
							<p class="font-semibold text-gray-800">{proyecto.presupuesto}</p>
						</div>
					</div>
				</div>
			)}

			{proyecto.duracion && (
				<div class="bg-orange-50 rounded-xl p-4">
					<div class="flex items-center">
						<span class="material-symbols-outlined text-orange-600 mr-2">
							schedule
						</span>
						<div>
							<p class="text-sm text-gray-600">Duración</p>
							<p class="font-semibold text-gray-800">{proyecto.duracion}</p>
						</div>
					</div>
				</div>
			)}

			{proyecto.estado && (
				<div class={`rounded-xl p-4 border ${getEstadoColor(proyecto.estado)}`}>
					<div class="flex items-center">
						<span class="material-symbols-outlined mr-2">
							flag
						</span>
						<div>
							<p class="text-sm opacity-75">Estado</p>
							<p class="font-semibold">{getEstadoTexto(proyecto.estado)}</p>
						</div>
					</div>
				</div>
			)}
		</div>

		<!-- Tecnologías utilizadas -->
		{proyecto.tecnologias && proyecto.tecnologias.length > 0 && (
			<div>
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-blue-600 mr-2">
						code
					</span>
					Tecnologías
				</h3>
				<div class="flex flex-wrap gap-2">
					{proyecto.tecnologias.map((tech) => (
						<span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
							{tech}
						</span>
					))}
				</div>
			</div>
		)}

		<!-- Características principales -->
		{proyecto.caracteristicas && proyecto.caracteristicas.length > 0 && (
			<div>
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-green-600 mr-2">
						star
					</span>
					Características Principales
				</h3>
				<ul class="space-y-2">
					{proyecto.caracteristicas.map((caracteristica) => (
						<li class="flex items-start">
							<span class="material-symbols-outlined text-green-600 mr-2 text-sm mt-0.5">
								check_circle
							</span>
							<span class="text-gray-700">{caracteristica}</span>
						</li>
					))}
				</ul>
			</div>
		)}

		<!-- Equipo -->
		{proyecto.equipo && proyecto.equipo.length > 0 && (
			<div>
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-purple-600 mr-2">
						group
					</span>
					Equipo
				</h3>
				<div class="flex flex-wrap gap-2">
					{proyecto.equipo.map((miembro) => (
						<span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
							{miembro}
						</span>
					))}
				</div>
			</div>
		)}

		<!-- Resultados -->
		{proyecto.resultados && proyecto.resultados.length > 0 && (
			<div>
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-orange-600 mr-2">
						trending_up
					</span>
					Resultados
				</h3>
				<ul class="space-y-2">
					{proyecto.resultados.map((resultado) => (
						<li class="flex items-start">
							<span class="material-symbols-outlined text-orange-600 mr-2 text-sm mt-0.5">
								arrow_forward
							</span>
							<span class="text-gray-700">{resultado}</span>
						</li>
					))}
				</ul>
			</div>
		)}

		<!-- Galería de imágenes -->
		{proyecto.imagenes && proyecto.imagenes.length > 0 && (
			<div>
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-indigo-600 mr-2">
						photo_library
					</span>
					Galería del Proyecto
				</h3>
				<div class="grid grid-cols-2 md:grid-cols-3 gap-3">
					{proyecto.imagenes.map((imagen, index) => (
						<img
							src={imagen}
							alt={`${proyecto.titulo} ${index + 1}`}
							class="rounded-lg object-cover h-32 w-full hover:scale-105 transition-transform cursor-pointer"
						/>
					))}
				</div>
			</div>
		)}

		<!-- CTA -->
		<div class="bg-gradient-to-r from-primary-50 to-blue-50 rounded-xl p-6 text-center">
			<p class="text-gray-700 mb-4">¿Interesado en un proyecto similar?</p>
			<button
				class="bg-primary-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary-700 transition-colors shadow-lg hover:shadow-xl"
				data-modal-target="citaModal"
				data-modal-close
			>
				Solicitar información
			</button>
		</div>
	</div>
</ModalBase>
