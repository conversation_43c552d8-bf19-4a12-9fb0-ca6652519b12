---
export interface Props {
	icon: string;
	title: string;
	description: string;
}

const { icon, title, description } = Astro.props;
---

<div
	class="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow"
>
	<div
		class="rounded-full bg-primary-100 w-14 h-14 flex items-center justify-center mb-4"
	>
		<span class="material-symbols-outlined text-primary-600 text-2xl"
			>{icon}</span
		>
	</div>
	<h3 class="font-bold text-xl mb-2">{title}</h3>
	<p class="text-gray-600">{description}</p>
</div>
