---
// Componente del drawer de navegación móvil
import Button01 from "./ui/buttons/Button01.astro";

interface Props {
	id?: string;
	class?: string;
	navItems?: Array<{ href: string; label: string }>;
}

const {
	id = "mobile-drawer",
	class: className = "",
	navItems = [
		{ href: "#inicio", label: "Inicio" },
		{ href: "#servicios", label: "Servicios" },
		{ href: "#equipo", label: "Equipo" },
		{ href: "#ubicacion", label: "Ubicación" },
		{ href: "#contacto", label: "Contacto" },
	],
} = Astro.props;
---

<!-- Overlay -->
<div class="fixed inset-0">
	<div
		id={`${id}-overlay`}
		class="drawer-overlay fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300"
		data-drawer-close
	>
		<!-- Drawer -->
		<div
			id={id}
			class={`drawer inset-0 h-full w-full bg-white shadow-lg z-50  
     ${className}`}
		>
			<!-- Header del drawer -->
			<div class="flex justify-between items-center p-6 border-b">
				<h2 class="text-xl font-semibold text-gray-800">Menú</h2>
				<button
					class="drawer-close p-2 hover:bg-gray-100 rounded-full transition-colors"
					data-drawer-close
					aria-label="Cerrar menú"
				>
					<span class="material-symbols-outlined text-gray-600">close</span
					>
				</button>
			</div>

			<!-- Navegación -->
			<nav class="p-6">
				<ul class="space-y-4">
					{
						navItems.map((item) => (
							<li>
								<a
									href={item.href}
									class="nav-link block text-lg text-gray-700 hover:text-primary-600 hover:bg-primary-50 px-4 py-3 rounded-lg transition-all duration-200 font-medium"
									data-drawer-close
								>
									{item.label}
								</a>
							</li>
						))
					}
				</ul>

				<!-- Botón de pedir cita en el drawer -->
				<div class="mt-8 pt-6 border-t">
					<div class="w-full" data-drawer-close>
						<Button01 textoBoton="Pedir Cita" modalTarget="citaModal" />
					</div>
				</div>
			</nav>
		</div>
	</div>
</div>

<style>
	.drawer-open {
		transform: translateX(0);
	}

	.drawer-overlay-visible {
		display: block;
	}
</style>

<script>
	// Script del drawer de navegación móvil
	document.addEventListener("DOMContentLoaded", () => {
		// Buscar todos los drawers en la página
		const drawers = document.querySelectorAll(
			'[id$="-drawer"], [id="mobile-drawer"]'
		);

		drawers.forEach((drawer) => {
			const drawerId = drawer.id;
			const overlay = document.getElementById(`${drawerId}-overlay`);

			if (!overlay) return;

			// Función para cerrar drawer (accesible desde el botón hamburguesa)
			function closeDrawer() {
				if (window.drawerControls && window.drawerControls[drawerId]) {
					window.drawerControls[drawerId].close();
				}
			}

			// Event listeners para cerrar drawer
			const closeButtons = drawer.querySelectorAll("[data-drawer-close]");
			closeButtons.forEach((btn) => {
				btn.addEventListener("click", (e) => {
					e.preventDefault();
					closeDrawer();
				});
			});

			// Cerrar al hacer click en overlay
			overlay.addEventListener("click", (e) => {
				if (e.target === overlay) {
					closeDrawer();
				}
			});

			// Cerrar drawer al hacer click en enlaces de navegación
			const navLinks = drawer.querySelectorAll(".nav-link");
			navLinks.forEach((link) => {
				link.addEventListener("click", () => {
					setTimeout(() => closeDrawer(), 100); // Pequeño delay para mejor UX
					// navegar al link después de cerrar el drawer
					setTimeout(() => {
						window.location.href = link.getAttribute("href") || "#";
					}, 120);
				});
			});
		});
	});
</script>
