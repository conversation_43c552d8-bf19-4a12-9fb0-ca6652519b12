---
/**
 * ModalFeature - Componente para mostrar detalles de características/funcionalidades
 * 
 * Este componente demuestra cómo extender el sistema de modales
 * para mostrar características específicas, funcionalidades o features.
 * 
 * Uso:
 * ```astro
 * <ModalFeature 
 *   id="modal-feature-sistema-citas" 
 *   feature={featureData} 
 * />
 * ```
 */
import ModalBase from "./ModalBase.astro";

export interface Props {
	id: string;
	feature: {
		titulo: string;
		descripcion: string;
		categoria?: string;
		version?: string;
		estado?: 'beta' | 'estable' | 'deprecated' | 'desarrollo';
		prioridad?: 'baja' | 'media' | 'alta' | 'critica';
		beneficios?: string[];
		requisitos?: string[];
		pasos?: string[];
		capturas?: string[];
		documentacion?: string;
		soporte?: string;
		fechaLanzamiento?: string;
		compatibilidad?: string[];
	};
}

const { id, feature } = Astro.props;

// Función para obtener el color del estado
const getEstadoColor = (estado: string) => {
	switch (estado) {
		case 'beta': return 'bg-yellow-50 text-yellow-700 border-yellow-200';
		case 'estable': return 'bg-green-50 text-green-700 border-green-200';
		case 'deprecated': return 'bg-red-50 text-red-700 border-red-200';
		case 'desarrollo': return 'bg-blue-50 text-blue-700 border-blue-200';
		default: return 'bg-gray-50 text-gray-700 border-gray-200';
	}
};

const getPrioridadColor = (prioridad: string) => {
	switch (prioridad) {
		case 'baja': return 'bg-gray-50 text-gray-700 border-gray-200';
		case 'media': return 'bg-blue-50 text-blue-700 border-blue-200';
		case 'alta': return 'bg-orange-50 text-orange-700 border-orange-200';
		case 'critica': return 'bg-red-50 text-red-700 border-red-200';
		default: return 'bg-gray-50 text-gray-700 border-gray-200';
	}
};

const getEstadoTexto = (estado: string) => {
	switch (estado) {
		case 'beta': return 'Beta';
		case 'estable': return 'Estable';
		case 'deprecated': return 'Obsoleto';
		case 'desarrollo': return 'En Desarrollo';
		default: return estado;
	}
};

const getPrioridadTexto = (prioridad: string) => {
	switch (prioridad) {
		case 'baja': return 'Baja';
		case 'media': return 'Media';
		case 'alta': return 'Alta';
		case 'critica': return 'Crítica';
		default: return prioridad;
	}
};
---

<ModalBase id={id} title={feature.titulo} size="lg">
	<div class="space-y-6">
		<!-- Descripción principal -->
		<div class="text-gray-700 leading-relaxed">
			<p>{feature.descripcion}</p>
		</div>

		<!-- Información básica de la feature -->
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			{feature.categoria && (
				<div class="bg-primary-50 rounded-xl p-4">
					<div class="flex items-center">
						<span class="material-symbols-outlined text-primary-600 mr-2">
							category
						</span>
						<div>
							<p class="text-sm text-gray-600">Categoría</p>
							<p class="font-semibold text-gray-800">{feature.categoria}</p>
						</div>
					</div>
				</div>
			)}

			{feature.version && (
				<div class="bg-blue-50 rounded-xl p-4">
					<div class="flex items-center">
						<span class="material-symbols-outlined text-blue-600 mr-2">
							tag
						</span>
						<div>
							<p class="text-sm text-gray-600">Versión</p>
							<p class="font-semibold text-gray-800">{feature.version}</p>
						</div>
					</div>
				</div>
			)}

			{feature.fechaLanzamiento && (
				<div class="bg-green-50 rounded-xl p-4">
					<div class="flex items-center">
						<span class="material-symbols-outlined text-green-600 mr-2">
							rocket_launch
						</span>
						<div>
							<p class="text-sm text-gray-600">Lanzamiento</p>
							<p class="font-semibold text-gray-800">{feature.fechaLanzamiento}</p>
						</div>
					</div>
				</div>
			)}

			{feature.estado && (
				<div class={`rounded-xl p-4 border ${getEstadoColor(feature.estado)}`}>
					<div class="flex items-center">
						<span class="material-symbols-outlined mr-2">
							info
						</span>
						<div>
							<p class="text-sm opacity-75">Estado</p>
							<p class="font-semibold">{getEstadoTexto(feature.estado)}</p>
						</div>
					</div>
				</div>
			)}

			{feature.prioridad && (
				<div class={`rounded-xl p-4 border ${getPrioridadColor(feature.prioridad)}`}>
					<div class="flex items-center">
						<span class="material-symbols-outlined mr-2">
							priority_high
						</span>
						<div>
							<p class="text-sm opacity-75">Prioridad</p>
							<p class="font-semibold">{getPrioridadTexto(feature.prioridad)}</p>
						</div>
					</div>
				</div>
			)}
		</div>

		<!-- Beneficios -->
		{feature.beneficios && feature.beneficios.length > 0 && (
			<div>
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-green-600 mr-2">
						thumb_up
					</span>
					Beneficios
				</h3>
				<ul class="space-y-2">
					{feature.beneficios.map((beneficio) => (
						<li class="flex items-start">
							<span class="material-symbols-outlined text-green-600 mr-2 text-sm mt-0.5">
								check_circle
							</span>
							<span class="text-gray-700">{beneficio}</span>
						</li>
					))}
				</ul>
			</div>
		)}

		<!-- Requisitos -->
		{feature.requisitos && feature.requisitos.length > 0 && (
			<div>
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-orange-600 mr-2">
						rule
					</span>
					Requisitos
				</h3>
				<ul class="space-y-2">
					{feature.requisitos.map((requisito) => (
						<li class="flex items-start">
							<span class="material-symbols-outlined text-orange-600 mr-2 text-sm mt-0.5">
								arrow_forward
							</span>
							<span class="text-gray-700">{requisito}</span>
						</li>
					))}
				</ul>
			</div>
		)}

		<!-- Pasos de uso -->
		{feature.pasos && feature.pasos.length > 0 && (
			<div>
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-blue-600 mr-2">
						list
					</span>
					Cómo usar esta funcionalidad
				</h3>
				<ol class="space-y-2">
					{feature.pasos.map((paso, index) => (
						<li class="flex items-start">
							<span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold mr-3 mt-0.5 flex-shrink-0">
								{index + 1}
							</span>
							<span class="text-gray-700">{paso}</span>
						</li>
					))}
				</ol>
			</div>
		)}

		<!-- Compatibilidad -->
		{feature.compatibilidad && feature.compatibilidad.length > 0 && (
			<div>
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-purple-600 mr-2">
						devices
					</span>
					Compatibilidad
				</h3>
				<div class="flex flex-wrap gap-2">
					{feature.compatibilidad.map((item) => (
						<span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
							{item}
						</span>
					))}
				</div>
			</div>
		)}

		<!-- Capturas de pantalla -->
		{feature.capturas && feature.capturas.length > 0 && (
			<div>
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-indigo-600 mr-2">
						screenshot
					</span>
					Capturas de pantalla
				</h3>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-3">
					{feature.capturas.map((captura, index) => (
						<img
							src={captura}
							alt={`${feature.titulo} captura ${index + 1}`}
							class="rounded-lg object-cover h-40 w-full hover:scale-105 transition-transform cursor-pointer border"
						/>
					))}
				</div>
			</div>
		)}

		<!-- Enlaces adicionales -->
		{(feature.documentacion || feature.soporte) && (
			<div class="bg-gray-50 rounded-xl p-4">
				<h3 class="font-semibold text-gray-800 mb-3 flex items-center">
					<span class="material-symbols-outlined text-gray-600 mr-2">
						link
					</span>
					Enlaces útiles
				</h3>
				<div class="space-y-2">
					{feature.documentacion && (
						<a
							href={feature.documentacion}
							target="_blank"
							class="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
						>
							<span class="material-symbols-outlined mr-2 text-sm">
								description
							</span>
							Ver documentación completa
						</a>
					)}
					{feature.soporte && (
						<a
							href={feature.soporte}
							target="_blank"
							class="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
						>
							<span class="material-symbols-outlined mr-2 text-sm">
								support
							</span>
							Obtener soporte
						</a>
					)}
				</div>
			</div>
		)}

		<!-- CTA -->
		<div class="bg-gradient-to-r from-primary-50 to-blue-50 rounded-xl p-6 text-center">
			<p class="text-gray-700 mb-4">¿Necesitas ayuda con esta funcionalidad?</p>
			<button
				class="bg-primary-600 text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary-700 transition-colors shadow-lg hover:shadow-xl"
				data-modal-target="citaModal"
				data-modal-close
			>
				Contactar soporte
			</button>
		</div>
	</div>
</ModalBase>
