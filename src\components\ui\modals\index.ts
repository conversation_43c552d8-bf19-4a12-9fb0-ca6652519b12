// Componentes base
export { default as ModalBase } from './ModalBase.astro';

// Modales específicos
export { default as ModalContacto } from './ModalContacto.astro';
export { default as ModalServicio } from './ModalServicio.astro';
export { default as ModalProyecto } from './ModalProyecto.astro';
export { default as ModalFeature } from './ModalFeature.astro';

// Tipos TypeScript
export type { Props as ModalBaseProps } from './ModalBase.astro';
export type { Props as ModalContactoProps } from './ModalContacto.astro';
export type { Props as ModalServicioProps } from './ModalServicio.astro';
export type { Props as ModalProyectoProps } from './ModalProyecto.astro';
export type { Props as ModalFeatureProps } from './ModalFeature.astro';

// Re-exportar utilidades de modales
export * from '../../../utils/modal-utils';

/**
 * Mapa de componentes de modales disponibles
 * Útil para renderizado dinámico de modales
 */
export const MODAL_COMPONENTS = {
	base: 'ModalBase',
	contacto: 'ModalContacto',
	servicio: 'ModalServicio',
	proyecto: 'ModalProyecto',
	feature: 'ModalFeature',
} as const;

/**
 * Tipos de modales disponibles
 */
export type AvailableModalTypes = keyof typeof MODAL_COMPONENTS;