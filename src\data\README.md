# Sistema de Servicios Reutilizable

Este sistema permite gestionar servicios de manera modular y reutilizable a través de archivos JSON y componentes Astro.

## Estructura del Sistema

### Archivos principales:
- `servicios.json` - Datos de configuración de servicios
- `servicios.ts` - Tipos TypeScript y utilidades
- `../components/Servicios.astro` - Componente principal que renderiza los servicios
- `../components/ui/modals/ModalServicio.astro` - Modal reutilizable para mostrar detalles

## Formato de Datos

### Estructura de un servicio:

```json
{
  "id": "identificador-unico",
  "image": "https://ejemplo.com/imagen.jpg",
  "title": "Título del Servicio",
  "description": "Descripción breve para la tarjeta",
  "alt": "Texto alternativo para la imagen",
  "servicio": {
    "titulo": "Título completo del servicio",
    "descripcion": "Descripción detallada que aparece en el modal",
    "icono": "nombre_del_icono_material_symbols",
    "precio": "Información de precios (opcional)",
    "duracion": "Duración del servicio (opcional)",
    "beneficios": ["Lista", "de", "beneficios"] // opcional,
    "proceso": ["Paso 1", "Paso 2", "Paso 3"] // opcional,
    "imagenes": ["url1.jpg", "url2.jpg"] // opcional
  }
}
```

### Campos obligatorios:
- `id`: Identificador único del servicio
- `image`: URL de la imagen principal
- `title`: Título corto para mostrar en la tarjeta
- `description`: Descripción breve para la tarjeta
- `alt`: Texto alternativo para la imagen
- `servicio.titulo`: Título completo del servicio
- `servicio.descripcion`: Descripción detallada
- `servicio.icono`: Nombre del icono (Material Symbols)

### Campos opcionales:
- `servicio.precio`: Información de precios
- `servicio.duracion`: Duración estimada
- `servicio.beneficios`: Array de beneficios
- `servicio.proceso`: Array de pasos del proceso
- `servicio.imagenes`: Array de URLs para galería

## Cómo usar el sistema

### 1. Agregar un nuevo servicio:

Edita el archivo `servicios.json` y agrega un nuevo objeto al array `servicios`:

```json
{
  "id": "nuevo-servicio",
  "image": "https://ejemplo.com/nueva-imagen.jpg",
  "title": "Nuevo Servicio",
  "description": "Descripción del nuevo servicio",
  "alt": "Nueva imagen de servicio",
  "servicio": {
    "titulo": "Nuevo Servicio Completo",
    "descripcion": "Descripción detallada del nuevo servicio...",
    "icono": "medical_services",
    "precio": "Desde 40€",
    "duracion": "45 minutos",
    "beneficios": [
      "Beneficio 1",
      "Beneficio 2"
    ],
    "proceso": [
      "Paso 1 del proceso",
      "Paso 2 del proceso"
    ]
  }
}
```

### 2. Modificar servicios existentes:

Simplemente edita los valores en el archivo `servicios.json`. Los cambios se reflejarán automáticamente en la aplicación.

### 3. Eliminar un servicio:

Elimina el objeto correspondiente del array `servicios` en el archivo JSON.

## Iconos disponibles

El sistema usa Material Symbols. Algunos iconos comunes:
- `medical_services` - Servicios médicos
- `analytics` - Análisis/estudios
- `directions_run` - Deportes
- `child_care` - Pediatría
- `elderly` - Geriatría
- `surgical` - Cirugía
- `healing` - Curación
- `health_and_safety` - Salud y seguridad

Ver más iconos en: https://fonts.google.com/icons

## Reutilización en otros proyectos

Para usar este sistema en otro proyecto:

1. **Copia los archivos base:**
   - `src/data/servicios.json`
   - `src/data/servicios.ts`
   - `src/components/ui/modals/ModalServicio.astro`
   - `src/components/ui/modals/ModalBase.astro` (dependencia)

2. **Adapta el componente principal:**
   - Copia `src/components/Servicios.astro` como base
   - Modifica los estilos según tu diseño
   - Ajusta la estructura HTML según tus necesidades

3. **Personaliza los datos:**
   - Edita `servicios.json` con tus propios servicios
   - Mantén la estructura de datos para compatibilidad

4. **Instala dependencias:**
   - Material Symbols (para iconos)
   - Tailwind CSS (para estilos, opcional)

## Validación de datos

El archivo `servicios.ts` incluye funciones de validación:

```typescript
import { validarServicios, getServicios } from './data/servicios';

const servicios = getServicios();
if (validarServicios(servicios)) {
  console.log('Todos los servicios son válidos');
} else {
  console.error('Hay servicios con estructura incorrecta');
}
```

## Mantenimiento

- **Backup**: Siempre haz backup del archivo `servicios.json` antes de cambios importantes
- **Validación**: Usa las funciones de validación para verificar la estructura
- **Imágenes**: Asegúrate de que las URLs de imágenes sean accesibles
- **Iconos**: Verifica que los nombres de iconos existan en Material Symbols

## Extensiones futuras

El sistema está diseñado para ser extensible:
- Agregar nuevos campos opcionales al tipo `ServicioDetalle`
- Crear nuevos tipos de modales especializados
- Implementar filtros y categorías
- Agregar soporte para múltiples idiomas
