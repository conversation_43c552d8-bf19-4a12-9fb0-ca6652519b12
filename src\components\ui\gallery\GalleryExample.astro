---
/**
 * GalleryExample - Ejemplo de uso de ImageGallery con lightbox
 */
import ImageGallery from "./ImageGallery.astro";

// Imágenes de ejemplo
const imagenesEjemplo = [
	"/hero_podial_1.png",
	"/logo_podial_1.png",
	"/logo_podial_texto_1.png",
];
---

<div class="p-8 space-y-8">
	<h2 class="text-2xl font-bold mb-6">
		Ejemplos de ImageGallery con Lightbox
	</h2>

	<!-- <PERSON>ría con lightbox habilitado (por defecto) -->
	<div class="space-y-4">
		<h3 class="text-lg font-semibold">Galería con Lightbox (por defecto)</h3>
		<ImageGallery imagenes={imagenesEjemplo} titulo="Ejemplo con Lightbox" />
	</div>

	<!-- Galería sin lightbox -->
	<div class="space-y-4">
		<h3 class="text-lg font-semibold">Galería sin Lightbox</h3>
		<ImageGallery
			imagenes={imagenesEjemplo}
			titulo="Ejemplo sin Lightbox"
			enableLightbox={false}
		/>
	</div>

	<!-- Galería sin título -->
	<div class="space-y-4">
		<h3 class="text-lg font-semibold">Galería sin Título</h3>
		<ImageGallery
			imagenes={imagenesEjemplo}
			titulo="Ejemplo sin título"
			showTitle={false}
		/>
	</div>

	<!-- Instrucciones de uso -->
	<div class="bg-blue-50 p-6 rounded-lg">
		<h3 class="text-lg font-semibold mb-3">
			Instrucciones de uso del Lightbox Mejorado:
		</h3>
		<ul class="space-y-2 text-gray-700">
			<li>
				• <strong>Click en imagen:</strong> Abre el lightbox a pantalla completa
			</li>
			<li>
				• <strong>Botón cerrar (X):</strong> Botón circular mejorado en la esquina
				superior derecha
			</li>
			<li>• <strong>Tecla ESC:</strong> Cierra el lightbox</li>
			<li>• <strong>Flechas ← →:</strong> Navega entre imágenes</li>
			<li>• <strong>Click en overlay:</strong> Cierra el lightbox</li>
			<li>
				• <strong>Botones de navegación:</strong> Botones circulares mejorados
				(solo si hay más de una imagen)
			</li>
			<li>
				• <strong>Contador:</strong> Muestra la imagen actual y total con fondo
				semitransparente
			</li>
		</ul>

		<div class="mt-4 p-4 bg-white rounded border-l-4 border-blue-500">
			<h4 class="font-semibold mb-2">Tamaños Responsive de Imagen:</h4>
			<ul class="text-sm space-y-1">
				<li>• <strong>Móvil:</strong> 90% del viewport</li>
				<li>• <strong>SM (640px+):</strong> 85% del viewport</li>
				<li>• <strong>MD (768px+):</strong> 80% del viewport</li>
				<li>• <strong>LG (1024px+):</strong> 75% del viewport</li>
				<li>• <strong>XL (1280px+):</strong> 70% del viewport</li>
			</ul>
		</div>
	</div>
</div>
