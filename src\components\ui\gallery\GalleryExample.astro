---
/**
 * GalleryExample - Ejemplo de uso de ImageGallery con lightbox
 */
import ImageGallery from "./ImageGallery.astro";

// Imágenes de ejemplo
const imagenesEjemplo = [
	"/hero_podial_1.png",
	"/logo_podial_1.png",
	"/logo_podial_texto_1.png",
];
---

<div class="p-8 space-y-8">
	<h2 class="text-2xl font-bold mb-6">Ejemplos de ImageGallery con Lightbox</h2>
	
	<!-- <PERSON>ría con lightbox habilitado (por defecto) -->
	<div class="space-y-4">
		<h3 class="text-lg font-semibold">Galería con Lightbox (por defecto)</h3>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Ejemplo con Lightbox" 
		/>
	</div>

	<!-- Galería sin lightbox -->
	<div class="space-y-4">
		<h3 class="text-lg font-semibold">Galería sin Lightbox</h3>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Ejemplo sin Lightbox" 
			enableLightbox={false}
		/>
	</div>

	<!-- Galería sin título -->
	<div class="space-y-4">
		<h3 class="text-lg font-semibold">Galería sin Título</h3>
		<ImageGallery 
			imagenes={imagenesEjemplo} 
			titulo="Ejemplo sin título" 
			showTitle={false}
		/>
	</div>

	<!-- Instrucciones de uso -->
	<div class="bg-blue-50 p-6 rounded-lg">
		<h3 class="text-lg font-semibold mb-3">Instrucciones de uso del Lightbox:</h3>
		<ul class="space-y-2 text-gray-700">
			<li>• <strong>Click en imagen:</strong> Abre el lightbox a pantalla completa</li>
			<li>• <strong>Tecla ESC:</strong> Cierra el lightbox</li>
			<li>• <strong>Flechas ← →:</strong> Navega entre imágenes</li>
			<li>• <strong>Click en overlay:</strong> Cierra el lightbox</li>
			<li>• <strong>Botones de navegación:</strong> Navega entre imágenes (solo si hay más de una)</li>
		</ul>
	</div>
</div>
